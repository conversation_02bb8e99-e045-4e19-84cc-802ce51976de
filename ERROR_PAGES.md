# Error Pages Documentation

This document outlines the error handling system implemented in the Hirelytics application, following the existing UI design patterns and components.

## Error Pages Created

### 1. **404 Not Found** (`/src/app/not-found.tsx`)

- **Route**: Automatically triggered for 404 errors
- **Icon**: FileQuestion (orange gradient)
- **Features**:
  - "Go Back" button using router.back()
  - "Go Home" button linking to "/"
  - Matches existing UI design with AuthCard and RadialGradientBackground

### 2. **General Error** (`/src/app/error.tsx`)

- **Route**: Catches runtime errors in page components
- **Icon**: AlertCircle (red gradient)
- **Features**:
  - "Try Again" button that calls the reset function
  - "Go Home" button
  - Displays error digest when available
  - Client-side error boundary

### 3. **Global Error** (`/src/app/global-error.tsx`)

- **Route**: Catches errors at the application level
- **Icon**: AlertTriangle (red-orange gradient)
- **Features**:
  - Full HTML structure (required for global errors)
  - "Reload App" button
  - "Go Home" button
  - Critical error handling

### 4. **403 Forbidden** (`/src/app/forbidden.tsx`)

- **Route**: `/forbidden` (custom route)
- **Icon**: ShieldX (red-orange gradient)
- **Features**:
  - "Try Login" button
  - "Go Home" button
  - Permission-based error messaging

### 5. **401 Unauthorized** (`/src/app/unauthorized.tsx`)

- **Route**: `/unauthorized` (custom route)
- **Icon**: UserX (amber-orange gradient)
- **Features**:
  - "Sign In" button linking to `/auth/login`
  - "Go Home" button
  - Authentication-focused messaging

### 6. **Loading Page** (`/src/app/loading.tsx`)

- **Route**: Automatically shown during page transitions
- **Features**:
  - Skeleton components matching the AuthCard layout
  - Animated logo and loading spinner
  - Consistent styling with error pages

## Utility Components

### 1. **ErrorPage Component** (`/src/components/ui/error-page.tsx`)

Reusable component for creating consistent error pages with:

- Configurable icons and gradients
- Custom actions
- Pre-configured variants for common error types
- TypeScript interface for consistency

```tsx
<ErrorPage
  title="Custom Error"
  description="Something went wrong"
  errorCode="500"
  icon={AlertTriangle}
  iconGradient="from-red-100 to-pink-100 dark:from-red-900/30 dark:to-pink-900/30"
  codeGradient="from-red-600 to-pink-600 dark:from-red-400 dark:to-pink-400"
  actions={<CustomActions />}
/>
```

### 2. **ErrorBoundary Component** (`/src/components/ui/error-boundary.tsx`)

React error boundary for catching JavaScript errors:

- Class component implementation
- Custom fallback UI option
- Higher-order component wrapper (`withErrorBoundary`)
- Error logging and handling

```tsx
// As a wrapper component
<ErrorBoundary onError={handleError}>
  <YourComponent />
</ErrorBoundary>;

// As a HOC
const SafeComponent = withErrorBoundary(YourComponent);
```

### 3. **useErrorHandler Hook** (`/src/hooks/use-error-handler.ts`)

Custom hook for programmatic error handling:

- HTTP status code mapping
- Router-based navigation to error pages
- Structured error information
- Consistent error logging

```tsx
const { handleError, handleHttpError } = useErrorHandler();

// Handle specific error types
handleError({ type: 'forbidden', message: 'Access denied' });

// Handle HTTP errors
handleHttpError(404, 'Resource not found');
```

## Design System Integration

All error pages use the existing Hirelytics design system:

### **Components Used**:

- `RadialGradientBackground` - Consistent background styling
- `AuthCard` - Card container with glass morphism effect
- `Button` - Styled buttons with hover effects and gradients
- `Skeleton` - Loading state components

### **Styling Patterns**:

- **Gradients**: Consistent color schemes (blue primary, red/orange for errors, amber for warnings)
- **Typography**: Geist Sans font family with proper hierarchy
- **Spacing**: 8-unit spacing system
- **Animations**: Subtle hover effects and transforms
- **Dark Mode**: Full dark mode support with appropriate color variants

### **Icons**:

- Lucide React icons with consistent sizing (w-10 h-10)
- Circular gradient backgrounds for visual hierarchy
- Color-coded by error type

## Usage Examples

### Programmatic Error Handling

```tsx
import { useErrorHandler } from '@/hooks/use-error-handler';

function MyComponent() {
  const { handleHttpError } = useErrorHandler();

  const fetchData = async () => {
    try {
      const response = await fetch('/api/data');
      if (!response.ok) {
        handleHttpError(response.status);
        return;
      }
      // Handle success
    } catch (error) {
      handleHttpError(500, 'Network error');
    }
  };
}
```

### Component Error Boundary

```tsx
import { withErrorBoundary } from '@/components/ui/error-boundary';

const MyComponent = () => {
  // Component that might throw errors
  return <div>Content</div>;
};

export default withErrorBoundary(MyComponent, {
  onError: (error, errorInfo) => {
    // Log to monitoring service
    console.error('Component error:', error, errorInfo);
  },
});
```

### Custom Error Page

```tsx
import { ErrorPage } from '@/components/ui/error-page';
import { Wifi } from 'lucide-react';

export default function NetworkError() {
  return (
    <ErrorPage
      title="Network Error"
      description="Please check your internet connection"
      icon={Wifi}
      iconGradient="from-blue-100 to-cyan-100 dark:from-blue-900/30 dark:to-cyan-900/30"
      codeGradient="from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400"
    />
  );
}
```

## Error Logging and Monitoring

All error pages include console logging for development and can be easily extended with:

- Error tracking services (Sentry, LogRocket, etc.)
- Analytics events
- User feedback collection
- Error reproduction data

## Accessibility Features

- **Semantic HTML**: Proper heading hierarchy and ARIA labels
- **Keyboard Navigation**: All interactive elements are keyboard accessible
- **Screen Reader Support**: Descriptive text and proper focus management
- **Color Contrast**: WCAG compliant color combinations
- **Reduced Motion**: Respects user preferences for animations

## Best Practices

1. **Use specific error pages** for known error types (401, 403, 404)
2. **Implement error boundaries** around components that might fail
3. **Log errors consistently** for debugging and monitoring
4. **Provide clear user actions** (retry, go home, login)
5. **Maintain design consistency** with the rest of the application
6. **Test error scenarios** in development and staging environments

## File Structure

```
src/
├── app/
│   ├── error.tsx                 # General error page
│   ├── global-error.tsx         # Global error page
│   ├── loading.tsx              # Loading page
│   ├── not-found.tsx            # 404 page
│   ├── forbidden.tsx            # 403 page
│   └── unauthorized.tsx         # 401 page
├── components/ui/
│   ├── error-boundary.tsx       # Error boundary component
│   └── error-page.tsx          # Reusable error page component
└── hooks/
    └── use-error-handler.ts     # Error handling hook
```

This error handling system provides a robust, consistent, and user-friendly way to handle various error scenarios while maintaining the Hirelytics design language and user experience.
