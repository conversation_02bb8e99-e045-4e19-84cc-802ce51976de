import { Plus } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

import { AddCandidateForm } from './_components/add-candidate-form';
import { CandidatesTable } from './_components/candidates-table';

export default function CandidatesPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Candidates</h1>
          <p className="text-muted-foreground">
            Manage candidate profiles and track their application progress
          </p>
        </div>
        <AddCandidateForm
          trigger={
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Candidate
            </Button>
          }
        />
      </div>

      {/* Candidates Table */}
      <Card>
        <CardContent>
          <CandidatesTable />
        </CardContent>
      </Card>
    </div>
  );
}
