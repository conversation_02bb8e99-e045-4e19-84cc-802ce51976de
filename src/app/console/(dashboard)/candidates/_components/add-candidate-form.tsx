'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2, Plus } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { type AddCandidateFormData, addCandidateSchema, type Candidate } from '@/@types/candidate';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { useCreateCandidate, useUpdateCandidate } from '@/hooks/use-candidates';

interface AddCandidateFormProps {
  candidate?: Candidate; // For editing mode
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function AddCandidateForm({
  candidate,
  trigger,
  open: controlledOpen,
  onOpenChange,
}: AddCandidateFormProps) {
  const [internalOpen, setInternalOpen] = useState(false);
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen;
  const setOpen = onOpenChange || setInternalOpen;

  const isEditing = !!candidate;

  const { mutate: createCandidate, isPending: isCreating } = useCreateCandidate();
  const { mutate: updateCandidate, isPending: isUpdating } = useUpdateCandidate();

  const form = useForm<AddCandidateFormData>({
    resolver: zodResolver(addCandidateSchema),
    defaultValues: {
      name: candidate?.name || '',
      email: candidate?.email || '',
      emailVerified: candidate?.emailVerified || false,
    },
  });

  // Reset form when candidate changes (for editing)
  useEffect(() => {
    if (candidate) {
      form.reset({
        name: candidate.name,
        email: candidate.email,
        emailVerified: candidate.emailVerified,
      });
    }
  }, [candidate, form]);

  const onSubmit = (data: AddCandidateFormData) => {
    if (isEditing && candidate) {
      // Update existing candidate
      updateCandidate(
        {
          id: candidate.id,
          name: data.name,
          email: data.email,
          emailVerified: data.emailVerified,
        },
        {
          onSuccess: (candidate) => {
            toast.success(`Candidate "${candidate.name}" has been updated successfully!`);
            form.reset();
            setOpen(false);
          },
          onError: (error) => {
            toast.error(error.message || 'Failed to update candidate. Please try again.');
          },
        }
      );
    } else {
      // Create new candidate
      createCandidate(
        {
          name: data.name,
          email: data.email,
          emailVerified: data.emailVerified,
        },
        {
          onSuccess: (candidate) => {
            toast.success(`Candidate "${candidate.name}" has been created successfully!`);
            form.reset();
            setOpen(false);
          },
          onError: (error) => {
            toast.error(error.message || 'Failed to create candidate. Please try again.');
          },
        }
      );
    }
  };

  const isPending = isCreating || isUpdating;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {!isEditing && (
        <DialogTrigger asChild>
          {trigger || (
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Candidate
            </Button>
          )}
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit Candidate' : 'Add New Candidate'}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update the candidate details below.'
              : 'Create a new candidate profile. All fields marked with an asterisk (*) are required.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name *</FormLabel>
                  <FormControl>
                    <Input placeholder="John Doe" {...field} />
                  </FormControl>
                  <FormDescription>The candidate&apos;s full name</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email *</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormDescription>The candidate&apos;s email address</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="emailVerified"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Email Verified</FormLabel>
                    <FormDescription>Mark this candidate&apos;s email as verified</FormDescription>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isPending
                  ? isEditing
                    ? 'Updating...'
                    : 'Creating...'
                  : isEditing
                    ? 'Update Candidate'
                    : 'Create Candidate'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
