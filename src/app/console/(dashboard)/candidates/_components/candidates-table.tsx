'use client';

import { type ColumnDef } from '@tanstack/react-table';
import { CheckCircle, Edit, Eye, MoreHorizontal, User, XCircle } from 'lucide-react';
import { useState } from 'react';

import type { Candidate, CandidateSortField } from '@/@types/candidate';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SortableHeader, TableWrapper } from '@/components/ui/table-wrapper';
import { useCandidates } from '@/hooks/use-candidates';
import { useTableState } from '@/hooks/use-table-state';

import { AddCandidateForm } from './add-candidate-form';
import { CandidateDetailsSheet } from './candidate-details-sheet';

export const candidatesColumns: ColumnDef<Candidate>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Candidate
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const candidate = row.original;
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={candidate.image} />
            <AvatarFallback>
              <User className="h-5 w-5" />
            </AvatarFallback>
          </Avatar>
          <div className="min-w-0 flex-1">
            <div className="font-medium truncate">{candidate.name}</div>
            <div className="text-sm text-muted-foreground truncate">{candidate.email}</div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Email
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const email = row.getValue('email') as string;
      return <div className="font-mono text-sm">{email}</div>;
    },
  },
  {
    accessorKey: 'emailVerified',
    header: 'Status',
    cell: ({ row }) => {
      const emailVerified = row.getValue('emailVerified') as boolean;
      return (
        <div className="flex items-center space-x-2">
          {emailVerified ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-500" />
              <Badge variant="outline" className="text-green-600 border-green-200">
                Verified
              </Badge>
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 text-red-500" />
              <Badge variant="outline" className="text-red-600 border-red-200">
                Unverified
              </Badge>
            </>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Created
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const createdAt = row.getValue('createdAt') as Date;
      return (
        <div className="text-sm text-muted-foreground">
          {new Date(createdAt).toLocaleDateString()}
        </div>
      );
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ({ row }) => {
      const candidate = row.original;
      return <CandidateActions candidate={candidate} />;
    },
  },
];

export function CandidatesTable() {
  const { apiParams } = useTableState<CandidateSortField>();

  const { data, isLoading, error, refetch } = useCandidates(apiParams);

  const candidates = data?.data ?? [];
  const pagination = data?.pagination;

  return (
    <TableWrapper
      columns={candidatesColumns}
      data={candidates}
      pagination={pagination}
      isLoading={isLoading}
      error={error}
      searchPlaceholder="Search candidates..."
      onRetry={() => refetch()}
    />
  );
}

interface CandidateActionsProps {
  candidate: Candidate;
}

function CandidateActions({ candidate }: CandidateActionsProps) {
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setDetailsOpen(true)}>
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setEditOpen(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <CandidateDetailsSheet
        candidateId={candidate.id}
        open={detailsOpen}
        onOpenChange={setDetailsOpen}
        onEdit={() => {
          setDetailsOpen(false);
          setEditOpen(true);
        }}
      />

      <AddCandidateForm candidate={candidate} open={editOpen} onOpenChange={setEditOpen} />
    </>
  );
}
