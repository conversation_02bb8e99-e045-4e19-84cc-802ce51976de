import { Plus } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { AddRecruiterForm } from './_components/add-recruiter-form';
import { RecruitersTable } from './_components/recruiters-table';

export default function RecruitersPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Recruiters</h1>
          <p className="text-muted-foreground">
            Manage recruiter accounts and their hiring activities
          </p>
        </div>
        <AddRecruiterForm
          trigger={
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Recruiter
            </Button>
          }
        />
      </div>

      {/* Recruiters Table */}
      <Card>
        <CardHeader>
          <CardTitle>Recruiters</CardTitle>
          <CardDescription>
            View and manage all recruiter accounts and their access levels
          </CardDescription>
        </CardHeader>
        <CardContent>
          <RecruitersTable />
        </CardContent>
      </Card>
    </div>
  );
}
