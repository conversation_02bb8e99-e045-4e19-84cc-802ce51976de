'use client';

import { type ColumnDef } from '@tanstack/react-table';
import { CheckCircle, Edit, Eye, MoreHorizontal, User, XCircle } from 'lucide-react';
import { useState } from 'react';

import type { <PERSON><PERSON><PERSON>er, RecruiterSortField } from '@/@types/recruiter';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SortableHeader, TableWrapper } from '@/components/ui/table-wrapper';
import { useRecruiters } from '@/hooks/use-recruiters';
import { useTableState } from '@/hooks/use-table-state';

import { AddRecruiterForm } from './add-recruiter-form';
import { RecruiterDetailsSheet } from './recruiter-details-sheet';

export const recruitersColumns: ColumnDef<Recruiter>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Recruiter
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const recruiter = row.original;
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={recruiter.image} />
            <AvatarFallback>
              <User className="h-5 w-5" />
            </AvatarFallback>
          </Avatar>
          <div className="min-w-0 flex-1">
            <div className="font-medium truncate">{recruiter.name}</div>
            <div className="text-sm text-muted-foreground truncate">{recruiter.email}</div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Email
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const email = row.getValue('email') as string;
      return <div className="font-mono text-sm">{email}</div>;
    },
  },
  {
    accessorKey: 'emailVerified',
    header: 'Status',
    cell: ({ row }) => {
      const emailVerified = row.getValue('emailVerified') as boolean;
      return (
        <div className="flex items-center space-x-2">
          {emailVerified ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-500" />
              <Badge variant="outline" className="text-green-600 border-green-200">
                Verified
              </Badge>
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 text-red-500" />
              <Badge variant="outline" className="text-red-600 border-red-200">
                Unverified
              </Badge>
            </>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Created
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const createdAt = row.getValue('createdAt') as Date;
      return (
        <div className="text-sm text-muted-foreground">
          {new Date(createdAt).toLocaleDateString()}
        </div>
      );
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ({ row }) => {
      const recruiter = row.original;
      return <RecruiterActions recruiter={recruiter} />;
    },
  },
];

export function RecruitersTable() {
  const { apiParams } = useTableState<RecruiterSortField>();

  const { data, isLoading, error, refetch } = useRecruiters(apiParams);

  const recruiters = data?.data ?? [];
  const pagination = data?.pagination;

  return (
    <TableWrapper
      columns={recruitersColumns}
      data={recruiters}
      pagination={pagination}
      isLoading={isLoading}
      error={error}
      searchPlaceholder="Search recruiters..."
      onRetry={() => refetch()}
    />
  );
}

interface RecruiterActionsProps {
  recruiter: Recruiter;
}

function RecruiterActions({ recruiter }: RecruiterActionsProps) {
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setDetailsOpen(true)}>
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setEditOpen(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <RecruiterDetailsSheet
        recruiterId={recruiter.id}
        open={detailsOpen}
        onOpenChange={setDetailsOpen}
        onEdit={() => {
          setDetailsOpen(false);
          setEditOpen(true);
        }}
      />

      <AddRecruiterForm recruiter={recruiter} open={editOpen} onOpenChange={setEditOpen} />
    </>
  );
}
