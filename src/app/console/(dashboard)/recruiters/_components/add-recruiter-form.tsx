'use client';

import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { Loader2, Plus } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import type { <PERSON><PERSON>ruiter } from '@/@types/recruiter';
import { type AddRecruiterFormData, addRecruiterSchema } from '@/@types/recruiter';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { useCreateRecruiter, useUpdateRecruiter } from '@/hooks/use-recruiters';

interface AddRecruiterFormProps {
  recruiter?: Recruiter; // For editing mode
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function AddRecruiterForm({
  recruiter,
  trigger,
  open: controlledOpen,
  onOpenChange,
}: AddRecruiterFormProps) {
  const [internalOpen, setInternalOpen] = useState(false);
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen;
  const setOpen = onOpenChange || setInternalOpen;

  const isEditing = !!recruiter;

  const { mutate: createRecruiter, isPending: isCreating } = useCreateRecruiter();
  const { mutate: updateRecruiter, isPending: isUpdating } = useUpdateRecruiter();

  const form = useForm<AddRecruiterFormData>({
    resolver: zodResolver(addRecruiterSchema),
    defaultValues: {
      name: recruiter?.name || '',
      email: recruiter?.email || '',
      emailVerified: recruiter?.emailVerified || false,
    },
  });

  // Reset form when recruiter changes (for editing)
  useEffect(() => {
    if (recruiter) {
      form.reset({
        name: recruiter.name,
        email: recruiter.email,
        emailVerified: recruiter.emailVerified,
      });
    }
  }, [recruiter, form]);

  const onSubmit = (data: AddRecruiterFormData) => {
    if (isEditing && recruiter) {
      // Update existing recruiter
      updateRecruiter(
        {
          id: recruiter.id,
          name: data.name,
          email: data.email,
          emailVerified: data.emailVerified,
        },
        {
          onSuccess: (recruiter) => {
            toast.success(`Recruiter "${recruiter.name}" has been updated successfully!`);
            form.reset();
            setOpen(false);
          },
          onError: (error) => {
            toast.error(error.message || 'Failed to update recruiter. Please try again.');
          },
        }
      );
    } else {
      // Create new recruiter
      createRecruiter(
        {
          name: data.name,
          email: data.email,
          emailVerified: data.emailVerified,
        },
        {
          onSuccess: (recruiter) => {
            toast.success(`Recruiter "${recruiter.name}" has been created successfully!`);
            form.reset();
            setOpen(false);
          },
          onError: (error) => {
            toast.error(error.message || 'Failed to create recruiter. Please try again.');
          },
        }
      );
    }
  };

  const isPending = isCreating || isUpdating;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {!isEditing && (
        <DialogTrigger asChild>
          {trigger || (
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Recruiter
            </Button>
          )}
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit Recruiter' : 'Add New Recruiter'}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update the recruiter details below.'
              : 'Create a new recruiter account. All fields marked with an asterisk (*) are required.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name *</FormLabel>
                  <FormControl>
                    <Input placeholder="Jane Smith" {...field} />
                  </FormControl>
                  <FormDescription>The recruiter&apos;s full name</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email *</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormDescription>The recruiter&apos;s email address</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="emailVerified"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Email Verified</FormLabel>
                    <FormDescription>Mark this recruiter&apos;s email as verified</FormDescription>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isPending
                  ? isEditing
                    ? 'Updating...'
                    : 'Creating...'
                  : isEditing
                    ? 'Update Recruiter'
                    : 'Create Recruiter'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
