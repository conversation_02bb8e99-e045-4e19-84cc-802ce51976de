'use client';

import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import * as React from 'react';

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

import { useRole } from '../../../../contexts/role-context';
import { getNavigationForRole } from '../../../../lib/navigation';
import { NavUser } from './nav-user';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { role, organization } = useRole();
  const pathname = usePathname();
  const navigationGroups = getNavigationForRole(role);

  const isActiveItem = (url: string) => {
    if (url === '/') {
      return pathname === '/console' || pathname === '/console/';
    }
    return pathname.startsWith(`/console${url}`);
  };

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-1.5">
              <Link href="/console">
                <div className="flex items-center gap-2">
                  <Image
                    src="/images/hirelytics-logo.svg"
                    alt="Hirelytics"
                    width={24}
                    height={24}
                    className="shrink-0"
                  />
                  <div className="flex flex-col">
                    <span className="text-base font-semibold">
                      {organization?.name || 'Hirelytics'}
                    </span>
                    <span className="text-xs text-muted-foreground capitalize">{role} Portal</span>
                  </div>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        {navigationGroups.map((group, index) => (
          <SidebarGroup key={index}>
            {group.title && (
              <SidebarGroupLabel className="text-xs font-medium text-muted-foreground/70">
                {group.title}
              </SidebarGroupLabel>
            )}
            <SidebarGroupContent>
              <SidebarMenu>
                {group.items.map((item) => {
                  const isActive = isActiveItem(item.url);
                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        asChild
                        tooltip={item.title}
                        className={`group relative overflow-hidden transition-all duration-200 px-1 py-3 ${
                          isActive
                            ? 'bg-primary/10 text-primary dark:bg-primary/15 dark:text-primary'
                            : 'hover:bg-slate-100 dark:hover:bg-slate-800/70'
                        }`}
                      >
                        <Link href={`${item.url}`}>
                          <div className="flex items-center gap-3">
                            <div
                              className={`p-1.5 rounded-md transition-colors ${
                                isActive
                                  ? `bg-primary/20 ${item.color || 'text-primary'}`
                                  : `bg-background/50 ${item.color || 'text-muted-foreground'}`
                              }`}
                            >
                              <item.icon className="size-4" />
                            </div>
                            <span
                              className={`text-sm font-medium ${isActive ? 'text-primary' : ''}`}
                            >
                              {item.title}
                            </span>
                          </div>
                          {/* Active indicator */}
                          {isActive && (
                            <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent dark:from-primary/15" />
                          )}
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>

      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
