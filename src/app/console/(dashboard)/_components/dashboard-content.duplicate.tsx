'use client';

import {
  IconBriefcase,
  IconBuilding,
  IconCalendar,
  IconFileText,
  IconHeart,
  IconTrendingDown,
  IconTrendingUp,
  IconUsers,
} from '@tabler/icons-react';
import { ChartPie as IconChart } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { useRole } from '../../../../contexts/role-context';

export function DashboardContent() {
  const { role } = useRole();

  const getDashboardData = () => {
    switch (role) {
      case 'admin':
        return {
          title: 'Admin Dashboard',
          description: 'Comprehensive overview of your recruitment platform',
          cards: [
            {
              title: 'Total Users',
              value: '12,345',
              change: '+12.5%',
              trend: 'up',
              icon: IconUsers,
              color: 'text-blue-500',
              bgColor: 'bg-blue-500/10',
            },
            {
              title: 'Active Jobs',
              value: '234',
              change: '+8.2%',
              trend: 'up',
              icon: IconBriefcase,
              color: 'text-orange-500',
              bgColor: 'bg-orange-500/10',
            },
            {
              title: 'Applications',
              value: '5,678',
              change: '+15.3%',
              trend: 'up',
              icon: IconFileText,
              color: 'text-teal-500',
              bgColor: 'bg-teal-500/10',
            },
            {
              title: 'Organizations',
              value: '89',
              change: '+4.1%',
              trend: 'up',
              icon: IconBuilding,
              color: 'text-purple-500',
              bgColor: 'bg-purple-500/10',
            },
          ],
        };

      case 'recruiter':
        return {
          title: 'Recruiter Dashboard',
          description: 'Track your recruitment performance and activities',
          cards: [
            {
              title: 'My Job Posts',
              value: '23',
              change: '+3',
              trend: 'up',
              icon: IconBriefcase,
              color: 'text-orange-500',
              bgColor: 'bg-orange-500/10',
            },
            {
              title: 'Applications',
              value: '456',
              change: '+12.5%',
              trend: 'up',
              icon: IconFileText,
              color: 'text-teal-500',
              bgColor: 'bg-teal-500/10',
            },
            {
              title: 'Interviews Scheduled',
              value: '18',
              change: '+6',
              trend: 'up',
              icon: IconCalendar,
              color: 'text-pink-500',
              bgColor: 'bg-pink-500/10',
            },
            {
              title: 'Candidates',
              value: '789',
              change: '+8.9%',
              trend: 'up',
              icon: IconUsers,
              color: 'text-blue-500',
              bgColor: 'bg-blue-500/10',
            },
          ],
        };

      case 'candidate':
        return {
          title: 'Candidate Dashboard',
          description: 'Track your job search progress and opportunities',
          cards: [
            {
              title: 'Job Applications',
              value: '12',
              change: '+3',
              trend: 'up',
              icon: IconFileText,
              color: 'text-teal-500',
              bgColor: 'bg-teal-500/10',
            },
            {
              title: 'Saved Jobs',
              value: '28',
              change: '+5',
              trend: 'up',
              icon: IconHeart,
              color: 'text-rose-500',
              bgColor: 'bg-rose-500/10',
            },
            {
              title: 'Interviews',
              value: '4',
              change: '+2',
              trend: 'up',
              icon: IconCalendar,
              color: 'text-pink-500',
              bgColor: 'bg-pink-500/10',
            },
            {
              title: 'Profile Views',
              value: '87',
              change: '+15.2%',
              trend: 'up',
              icon: IconChart,
              color: 'text-violet-500',
              bgColor: 'bg-violet-500/10',
            },
          ],
        };

      default:
        return {
          title: 'Dashboard',
          description: 'Welcome to your dashboard',
          cards: [],
        };
    }
  };

  const data = getDashboardData();

  return (
    <div className="flex flex-col space-y-6 p-6 ">
      {/* Header */}
      <div className="space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">{data.title}</h2>
        <p className="text-muted-foreground">{data.description}</p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {data.cards.map((card, index) => (
          <Card
            key={index}
            className="relative overflow-hidden transition-all duration-200 hover:shadow-md"
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-md ${card.bgColor}`}>
                <card.icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-2xl font-bold">{card.value}</div>
                <Badge
                  variant="outline"
                  className={`gap-1 ${card.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}
                >
                  {card.trend === 'up' ? (
                    <IconTrendingUp className="h-3 w-3" />
                  ) : (
                    <IconTrendingDown className="h-3 w-3" />
                  )}
                  {card.change}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {card.trend === 'up' ? 'Increased' : 'Decreased'} from last month
              </p>
            </CardContent>

            {/* Gradient overlay */}
            <div
              className={`absolute inset-0 bg-gradient-to-br ${card.bgColor} opacity-0 transition-opacity duration-200 hover:opacity-5`}
            />
          </Card>
        ))}
      </div>

      {/* Role-specific content */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Your latest activities and updates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {role === 'admin' && (
                <>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                    <IconUsers className="h-5 w-5 text-blue-500" />
                    <div>
                      <p className="font-medium">New recruiter joined</p>
                      <p className="text-sm text-muted-foreground">Sarah Johnson from TechCorp</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                    <IconBriefcase className="h-5 w-5 text-orange-500" />
                    <div>
                      <p className="font-medium">New job posted</p>
                      <p className="text-sm text-muted-foreground">
                        Senior Developer at StartupXYZ
                      </p>
                    </div>
                  </div>
                </>
              )}

              {role === 'recruiter' && (
                <>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                    <IconFileText className="h-5 w-5 text-teal-500" />
                    <div>
                      <p className="font-medium">New application received</p>
                      <p className="text-sm text-muted-foreground">Frontend Developer position</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                    <IconCalendar className="h-5 w-5 text-pink-500" />
                    <div>
                      <p className="font-medium">Interview scheduled</p>
                      <p className="text-sm text-muted-foreground">Tomorrow at 2:00 PM</p>
                    </div>
                  </div>
                </>
              )}

              {role === 'candidate' && (
                <>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                    <IconFileText className="h-5 w-5 text-teal-500" />
                    <div>
                      <p className="font-medium">Application status updated</p>
                      <p className="text-sm text-muted-foreground">
                        Software Engineer - Under Review
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
                    <IconCalendar className="h-5 w-5 text-pink-500" />
                    <div>
                      <p className="font-medium">Interview invitation</p>
                      <p className="text-sm text-muted-foreground">UI/UX Designer at DesignCorp</p>
                    </div>
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks you might want to perform</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {role === 'admin' && (
                <>
                  <button className="w-full p-3 text-left rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors">
                    <p className="font-medium">Add New User</p>
                    <p className="text-sm text-muted-foreground">
                      Create admin or recruiter account
                    </p>
                  </button>
                  <button className="w-full p-3 text-left rounded-lg bg-muted/50 hover:bg-muted transition-colors">
                    <p className="font-medium">View Analytics</p>
                    <p className="text-sm text-muted-foreground">Platform performance metrics</p>
                  </button>
                </>
              )}

              {role === 'recruiter' && (
                <>
                  <button className="w-full p-3 text-left rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors">
                    <p className="font-medium">Post New Job</p>
                    <p className="text-sm text-muted-foreground">Create a new job listing</p>
                  </button>
                  <button className="w-full p-3 text-left rounded-lg bg-muted/50 hover:bg-muted transition-colors">
                    <p className="font-medium">Review Applications</p>
                    <p className="text-sm text-muted-foreground">Check pending applications</p>
                  </button>
                </>
              )}

              {role === 'candidate' && (
                <>
                  <button className="w-full p-3 text-left rounded-lg bg-primary/10 hover:bg-primary/20 transition-colors">
                    <p className="font-medium">Search Jobs</p>
                    <p className="text-sm text-muted-foreground">Find your next opportunity</p>
                  </button>
                  <button className="w-full p-3 text-left rounded-lg bg-muted/50 hover:bg-muted transition-colors">
                    <p className="font-medium">Update Profile</p>
                    <p className="text-sm text-muted-foreground">Keep your profile current</p>
                  </button>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
