import { IconBriefcase, IconBuilding, IconUsers } from '@tabler/icons-react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { api } from '@/trpc/server';

export async function DashboardContent({ role: _role }: { role: string }) {
  const dashboard = await api.dashboard.getDashboardStats();
  const stats = dashboard.stats;

  return (
    <div className="flex flex-col space-y-6 p-6 ">
      {/* Header */}
      <div className="space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">Admin Dashboard</h2>
        <p className="text-muted-foreground">Comprehensive overview of your recruitment platform</p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="relative overflow-hidden transition-all duration-200 hover:shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Users</CardTitle>
            <div className={`p-2 rounded-md bg-blue-500/10`}>
              <IconUsers className={`h-4 w-4 text-blue-500`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{stats.totalUsers}</div>
            </div>
          </CardContent>
        </Card>
        <Card className="relative overflow-hidden transition-all duration-200 hover:shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Recruiters
            </CardTitle>
            <div className={`p-2 rounded-md bg-teal-500/10`}>
              <IconUsers className={`h-4 w-4 text-teal-500`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{stats.totalRecruiters}</div>
            </div>
          </CardContent>
        </Card>
        <Card className="relative overflow-hidden transition-all duration-200 hover:shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Organizations
            </CardTitle>
            <div className={`p-2 rounded-md bg-purple-500/10`}>
              <IconBuilding className={`h-4 w-4 text-purple-500`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{stats.totalOrganizations}</div>
            </div>
          </CardContent>
        </Card>
        <Card className="relative overflow-hidden transition-all duration-200 hover:shadow-md">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Jobs</CardTitle>
            <div className={`p-2 rounded-md bg-orange-500/10`}>
              <IconBriefcase className={`h-4 w-4 text-orange-500`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold">{stats.totalJobs}</div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
