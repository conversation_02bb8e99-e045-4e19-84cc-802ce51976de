import { Receipt } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

export default function ProfilePage() {
  return (
    <div>
      <section>
        <div className="flex items-center gap-3 mb-6">
          <div className="p-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
            <Receipt className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
          </div>
          <div>
            <h2 className="text-2xl font-semibold tracking-tight">Billing</h2>
            <p className="text-muted-foreground">Manage your subscription and payment methods.</p>
          </div>
        </div>

        <div className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardContent className="p-8">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-3">
                    <h3 className="text-xl font-semibold">Pro Plan</h3>
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Active</Badge>
                  </div>
                  <p className="text-muted-foreground">
                    $19/month • Next billing: January 15, 2024
                  </p>
                </div>
                <Button size="lg">Manage Plan</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
