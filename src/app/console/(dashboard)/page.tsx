import { headers } from 'next/headers';

import { auth } from '@/auth';

import AccountChooser from '../_components/account-chooser';
import { DashboardContent } from './_components/dashboard-content';

export default async function AuthPage() {
  const headerlist = await headers();
  const session = await auth.api.getSession({
    headers: headerlist,
  });

  const role = session?.user?.role || 'user'; // Default to 'user' if role is not defined

  if (!session) {
    return <AccountChooser />;
  }

  return <DashboardContent role={role} />;
}
