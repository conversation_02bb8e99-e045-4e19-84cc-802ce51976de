import { Card, CardContent } from '@/components/ui/card';

import { PortalRequestsTable } from './_components/portal-request-table';

export default function PortalRequestPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Portal Request</h1>
          <p className="text-muted-foreground">
            Portal requests allow recruiters to request access to your talent pool. You can review
            and manage these requests here.
          </p>
        </div>
      </div>

      <Card>
        <CardContent>
          <PortalRequestsTable />
        </CardContent>
      </Card>
    </div>
  );
}
