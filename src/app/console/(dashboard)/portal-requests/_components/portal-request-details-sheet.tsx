'use client';

import { format, isValid } from 'date-fns';
import { Calendar, User } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { VisuallyHidden } from '@/components/ui/visually-hidden';
import { usePortalRequest } from '@/hooks/use-portal-request';

// Helper function to safely format dates
function formatDate(dateValue: string | Date | undefined | null): string {
  if (!dateValue) return 'Invalid date';

  const date = new Date(dateValue);
  if (!isValid(date)) return 'Invalid date';

  return format(date, 'PPP p');
}

interface PortalRequestDetailsSheetProps {
  portalRequestId: string | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PortalRequestDetailsSheet({
  portalRequestId,
  open,
  onOpenChange,
}: PortalRequestDetailsSheetProps) {
  const {
    data: portalRequest,
    isLoading,
    error,
  } = usePortalRequest(portalRequestId || '', open && !!portalRequestId);

  if (isLoading) {
    return (
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent className="min-w-[400px] sm:min-w-[540px]">
          <SheetHeader>
            <VisuallyHidden>
              <SheetTitle>Loading Portal Request Details</SheetTitle>
            </VisuallyHidden>
          </SheetHeader>
          <div className="flex items-center justify-center h-full">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-sm text-muted-foreground">Loading portalRequest details...</p>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  if (error || !portalRequest) {
    return (
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent className="min-w-[400px] sm:min-w-[540px]">
          <SheetHeader>
            <VisuallyHidden>
              <SheetTitle>Portal Request Access Details Error</SheetTitle>
            </VisuallyHidden>
          </SheetHeader>
          <div className="flex items-center justify-center h-full">
            <div className="text-center space-y-4">
              <div className="h-12 w-12 rounded-full bg-red-100 dark:bg-red-950 mx-auto flex items-center justify-center">
                <svg
                  className="h-6 w-6 text-red-600 dark:text-red-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.502 0L4.312 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <div>
                <p className="font-medium text-foreground">Failed to load portalRequest</p>
                <p className="text-sm text-muted-foreground">
                  The portalRequest details could not be retrieved
                </p>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="min-w-[400px] sm:min-w-[540px] overflow-y-auto">
        <SheetHeader className="pb-6 border-b">
          <SheetTitle className="text-xl font-semibold text-left">
            Portal Request Details
          </SheetTitle>
          <SheetDescription className="text-left">
            View detailed information about this portalRequest
          </SheetDescription>
        </SheetHeader>

        <div className="px-1 py-6 space-y-8 relative">
          {/* Candidate Header */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-lg p-6 border border-blue-200/50 dark:border-blue-800/50">
            <div className="flex items-center space-x-4">
              {/* <Avatar className="h-20 w-20 border-2 border-background shadow-lg">
                <AvatarImage src={portalRequest.image} />
                <AvatarFallback className="text-xl font-semibold bg-gradient-to-br from-blue-500 to-indigo-600 text-white">
                  <User className="h-10 w-10" />
                </AvatarFallback>
              </Avatar> */}
              {/* <div className="flex-1">
                <h3 className="text-2xl font-bold text-foreground">{portalRequest.}</h3>
                <div className="flex items-center space-x-2 mt-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">{portalRequest.email}</span>
                </div>
              </div>*/}
            </div>
          </div>

          {/* Basic Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-blue-600 dark:text-blue-400 flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Basic Information</span>
            </h4>
            <div className="bg-card rounded-lg border p-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                <div className="space-y-1">
                  <label className="font-medium text-muted-foreground text-xs uppercase tracking-wider">
                    Portal Request ID
                  </label>
                  <p className="font-mono text-sm bg-muted/50 rounded px-2 py-1">
                    {portalRequest.id}
                  </p>
                </div>
                <div className="space-y-1">
                  <label className="font-medium text-muted-foreground text-xs uppercase tracking-wider">
                    Role
                  </label>
                  <div>
                    <Badge
                      variant="secondary"
                      className="mt-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                    >
                      {/* {portalRequest.role} */}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Email Status */}

          {/* Timeline */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-purple-600 dark:text-purple-400 flex items-center space-x-2">
              <Calendar className="h-5 w-5" />
              <span>Timeline</span>
            </h4>
            <div className="bg-card rounded-lg border p-4">
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="h-8 w-8 rounded-full bg-green-100 dark:bg-green-950 flex items-center justify-center mt-1">
                    <Calendar className="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-foreground">Created</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(portalRequest.createdAt)}
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-950 flex items-center justify-center mt-1">
                    <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-foreground">Last Updated</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(portalRequest.updatedAt)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
