'use client';

import { type ColumnDef } from '@tanstack/react-table';
import { Eye, MoreHorizontal } from 'lucide-react';
import { useState } from 'react';

import { PortalRequestAccess, PortalRequestSortField } from '@/@types/portal-request-access';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SortableHeader, TableWrapper } from '@/components/ui/table-wrapper';
import { usePortalRequests } from '@/hooks/use-portal-request';
import { useTableState } from '@/hooks/use-table-state';

import { PortalRequestDetailsSheet } from './portal-request-details-sheet';

export const portalRequestsColumns: ColumnDef<PortalRequestAccess>[] = [
  {
    accessorKey: 'full_name',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Full Name
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const fullName = row.getValue('full_name') as string;
      return <div className="text-sm">{fullName}</div>;
    },
  },
  {
    accessorKey: 'work_email',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Work Email
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const workEmail = row.getValue('work_email') as string;
      return <div className="text-sm text-muted-foreground">{workEmail}</div>;
    },
  },
  {
    accessorKey: 'company_name',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Company Name
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const companyName = row.getValue('company_name') as string;
      return <div className="text-sm">{companyName}</div>;
    },
  },
  {
    accessorKey: 'company_size',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Company Size
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const companySize = row.getValue('company_size') as string;
      return <div className="text-sm">{companySize}</div>;
    },
  },
  {
    accessorKey: 'industry',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Industry
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const industry = row.getValue('industry') as string;
      return <div className="text-sm">{industry}</div>;
    },
  },
  {
    accessorKey: 'status',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Status
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      return <div className="text-sm">{status}</div>;
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Created
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const createdAt = row.getValue('createdAt') as Date;
      return (
        <div className="text-sm text-muted-foreground">
          {new Date(createdAt).toLocaleDateString()}
        </div>
      );
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ({ row }) => {
      const portalRequest = row.original;
      return <PortalRequestActions portalRequest={portalRequest} />;
    },
  },
];

export function PortalRequestsTable() {
  const { apiParams } = useTableState<PortalRequestSortField>();

  const validSortByValues: PortalRequestSortField[] = [
    'createdAt',
    'updatedAt',
    'full_name',
    'work_email',
    'company_name',
    'company_size',
    'industry',
    'status',
  ];
  if (!validSortByValues.includes(apiParams.sortBy)) {
    apiParams.sortBy = 'createdAt'; // Default to 'createdAt' if invalid
    apiParams.sortOrder = 'asc'; // Default to ascending order
  }

  const { data, isLoading, error, refetch } = usePortalRequests(apiParams);

  const portalRequests = data?.data ?? [];
  const pagination = data?.pagination;

  return (
    <TableWrapper
      columns={portalRequestsColumns}
      data={portalRequests}
      pagination={pagination}
      isLoading={isLoading}
      error={error}
      searchPlaceholder="Search Portal Requests Access..."
      onRetry={() => refetch()}
    />
  );
}

interface PortalRequestActionsProps {
  portalRequest: PortalRequestAccess;
}

function PortalRequestActions({ portalRequest }: PortalRequestActionsProps) {
  const [detailsOpen, setDetailsOpen] = useState(false);

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setDetailsOpen(true)}>
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <PortalRequestDetailsSheet
        portalRequestId={portalRequest.id}
        open={detailsOpen}
        onOpenChange={setDetailsOpen}
      />
    </>
  );
}
