import { Check, Edit, Save, Trash2, X } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { toast } from 'sonner';

import type { FormValues, Question, QuestionCategoryConfig, QuestionType } from '@/@types/job';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { FormField, FormItem, FormMessage } from '@/components/ui/form';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';

import { questionModes, questionTypesByIndustry } from './data/job-form-constants';
import { generateAIQuestions } from './utils/ai-helpers';

interface QuestionsStepProps {
  form: UseFormReturn<FormValues>;
  onValidationChange?: (isValid: boolean) => void;
}

export function QuestionsStep({ form, onValidationChange }: QuestionsStepProps) {
  const [newQuestionType, setNewQuestionType] = useState('');
  const [newQuestionText, setNewQuestionText] = useState('');
  const [showAIConfirmDialog, setShowAIConfirmDialog] = useState(false);
  const [editingQuestionId, setEditingQuestionId] = useState<string | null>(null);
  const [editingQuestionText, setEditingQuestionText] = useState('');
  const [editingQuestionType, setEditingQuestionType] = useState('');

  const currentMode = form.watch('questionsConfig.mode');
  const questionsConfig = form.watch('questionsConfig');

  const handleModeChange = (mode: 'manual' | 'ai-mode') => {
    form.setValue('questionsConfig.mode', mode, { shouldDirty: true });
    // Reset questions when mode changes
    form.setValue('questionsConfig.questions', [], { shouldDirty: true });
    // Reset categories when mode changes
    form.setValue('questionsConfig.questionTypes', [], { shouldDirty: true });
    form.setValue('questionsConfig.categoryConfigs', [], { shouldDirty: true });
    form.setValue('questionsConfig.totalQuestions', 0, { shouldDirty: true });
  };

  const updateCategoryConfigs = (selectedTypes: string[]) => {
    const currentConfigs = form.getValues('questionsConfig.categoryConfigs') || [];
    const newConfigs: QuestionCategoryConfig[] = selectedTypes.map((type) => {
      const existingConfig = currentConfigs.find((config) => config.type === type);
      return existingConfig || { type, numberOfQuestions: 1 };
    });

    // Calculate total questions - sum of all categories
    const totalQuestions = newConfigs.reduce((sum, config) => sum + config.numberOfQuestions, 0);

    form.setValue('questionsConfig.categoryConfigs', newConfigs, { shouldDirty: true });
    form.setValue('questionsConfig.totalQuestions', totalQuestions, { shouldDirty: true });
  };

  const updateCategoryQuestionCount = (categoryType: string, count: number) => {
    const currentConfigs = form.getValues('questionsConfig.categoryConfigs') || [];
    const updatedConfigs = currentConfigs.map((config) =>
      config.type === categoryType ? { ...config, numberOfQuestions: Math.max(1, count) } : config
    );

    // Calculate new total - sum of all categories
    const totalQuestions = updatedConfigs.reduce(
      (sum, config) => sum + config.numberOfQuestions,
      0
    );

    form.setValue('questionsConfig.categoryConfigs', updatedConfigs, { shouldDirty: true });
    form.setValue('questionsConfig.totalQuestions', totalQuestions, { shouldDirty: true });
  };

  const handleAddManualQuestion = () => {
    if (!newQuestionText.trim()) {
      toast.error('Please enter a question');
      return;
    }

    if (!newQuestionType || newQuestionType === 'no-categories') {
      toast.error('Please select a valid question type');
      return;
    }

    const availableTypes = getSelectedQuestionTypes();
    if (!availableTypes.some((type) => type.value === newQuestionType)) {
      toast.error(
        'Selected question type is not available. Please select from available categories.'
      );
      return;
    }

    const currentQuestions = form.getValues('questionsConfig.questions') || [];
    const totalRequested = questionsConfig.totalQuestions || 0;

    // Check if we've reached the maximum number of questions
    if (currentQuestions.length >= totalRequested) {
      toast.error(`You can only add ${totalRequested} questions. Maximum limit reached.`);
      return;
    }

    const newQuestion: Question = {
      id: `manual-${Date.now()}`,
      type: newQuestionType,
      question: newQuestionText.trim(),
      isAIGenerated: false,
    };

    form.setValue('questionsConfig.questions', [...currentQuestions, newQuestion], {
      shouldDirty: true,
    });

    // Reset form
    setNewQuestionText('');
    const availableTypesForReset = getSelectedQuestionTypes();
    setNewQuestionType(availableTypesForReset.length > 0 ? availableTypesForReset[0].value : '');
    toast.success('Question added successfully');
  };

  const handleRemoveQuestion = (questionId: string) => {
    const currentQuestions = form.getValues('questionsConfig.questions') || [];
    const updatedQuestions = currentQuestions.filter((q) => q.id !== questionId);
    form.setValue('questionsConfig.questions', updatedQuestions, { shouldDirty: true });
  };

  const handleEditQuestion = (question: Question) => {
    setEditingQuestionId(question.id);
    setEditingQuestionText(question.question);
    setEditingQuestionType(question.type);
  };

  const handleSaveEdit = () => {
    if (!editingQuestionText.trim() || !editingQuestionId) return;

    const currentQuestions = form.getValues('questionsConfig.questions') || [];
    const updatedQuestions = currentQuestions.map((q) =>
      q.id === editingQuestionId
        ? { ...q, question: editingQuestionText.trim(), type: editingQuestionType }
        : q
    );

    form.setValue('questionsConfig.questions', updatedQuestions, { shouldDirty: true });
    setEditingQuestionId(null);
    setEditingQuestionText('');
    const availableTypesForSave = getSelectedQuestionTypes();
    setEditingQuestionType(availableTypesForSave.length > 0 ? availableTypesForSave[0].value : '');
    toast.success('Question updated successfully');
  };

  const handleCancelEdit = () => {
    setEditingQuestionId(null);
    setEditingQuestionText('');
    const availableTypesForCancel = getSelectedQuestionTypes();
    setEditingQuestionType(
      availableTypesForCancel.length > 0 ? availableTypesForCancel[0].value : ''
    );
  };

  const handleGenerateAIQuestions = async () => {
    const jobTitle = form.watch('title');
    const industry = form.watch('industry');
    const skills = form.watch('skills');

    if (!jobTitle || !industry || !skills.length) {
      toast.error('Please fill in job title, industry, and skills first');
      return;
    }

    const currentQuestions = form.getValues('questionsConfig.questions') || [];

    // If there are existing questions, show confirmation dialog
    if (currentQuestions.length > 0) {
      setShowAIConfirmDialog(true);
      return;
    }

    await generateQuestions();
  };

  const generateQuestions = async () => {
    try {
      const jobTitle = form.watch('title');
      const industry = form.watch('industry');
      const skills = form.watch('skills');
      const categoryConfigs = form.getValues('questionsConfig.categoryConfigs') || [];
      const generatedQuestions: Question[] = [];

      for (const config of categoryConfigs) {
        if (config.numberOfQuestions > 0) {
          const questionsForCategory = await generateAIQuestions(
            config.numberOfQuestions,
            [config.type],
            jobTitle,
            industry,
            skills
          );
          generatedQuestions.push(...questionsForCategory);
        }
      }

      // Replace existing questions with new ones
      form.setValue('questionsConfig.questions', generatedQuestions, { shouldDirty: true });
      toast.success(`Generated ${generatedQuestions.length} questions successfully`);
    } catch (error) {
      console.error('Error generating questions:', error);
      toast.error('Failed to generate questions. Please try again.');
    }
  };

  const handleConfirmAIGeneration = async () => {
    setShowAIConfirmDialog(false);
    await generateQuestions();
  };

  const getQuestionTypesForIndustry = useCallback((): QuestionType[] => {
    const selectedIndustry = form.watch('industry');
    return selectedIndustry
      ? questionTypesByIndustry[selectedIndustry as keyof typeof questionTypesByIndustry] ||
          questionTypesByIndustry.other
      : questionTypesByIndustry.other;
  }, [form]);

  // Get only the question types that are selected in categories
  const getSelectedQuestionTypes = useCallback((): QuestionType[] => {
    const availableTypes = getQuestionTypesForIndustry();
    const selectedCategories = form.watch('questionsConfig.questionTypes') || [];

    return availableTypes.filter((type) => selectedCategories.includes(type.value));
  }, [form, getQuestionTypesForIndustry]);

  // Update default question type when selected categories change
  useEffect(() => {
    const selectedTypes = getSelectedQuestionTypes();
    if (selectedTypes.length > 0 && !newQuestionType) {
      setNewQuestionType(selectedTypes[0].value);
    }
    if (selectedTypes.length === 0) {
      setNewQuestionType('');
    }
  }, [questionsConfig.questionTypes, newQuestionType, getSelectedQuestionTypes]);

  const getQuestionTypeLabel = (type: string): string => {
    const availableQuestionTypes = getQuestionTypesForIndustry();
    return availableQuestionTypes.find((t) => t.value === type)?.label || type;
  };

  const getManualQuestionCounts = useCallback(() => {
    const totalRequested = questionsConfig.totalQuestions || 0;
    const currentQuestions = questionsConfig.questions || [];
    const totalEntered = currentQuestions.length;
    const remaining = Math.max(0, totalRequested - totalEntered);

    return {
      totalRequested,
      totalEntered,
      remaining,
      isComplete: totalEntered >= totalRequested && totalRequested > 0,
    };
  }, [questionsConfig.totalQuestions, questionsConfig.questions]);

  // Call validation callback whenever form state changes
  useEffect(() => {
    if (onValidationChange) {
      const validateQuestionsStep = () => {
        if (currentMode === 'manual') {
          const counts = getManualQuestionCounts();
          // For manual mode, we need questions and they should meet the requirements
          const hasQuestions = (questionsConfig.questions?.length || 0) > 0;
          const meetsRequirements = counts.isComplete;
          const hasCategories = (questionsConfig.questionTypes?.length || 0) > 0;

          return hasQuestions && meetsRequirements && hasCategories;
        }
        // For AI mode, validation is different (categories selected, etc.)
        const hasCategories = (questionsConfig.questionTypes?.length || 0) > 0;
        const hasTotalQuestions = (questionsConfig.totalQuestions || 0) > 0;

        return hasCategories && hasTotalQuestions;
      };

      const isValid = validateQuestionsStep();
      onValidationChange(isValid);
    }
  }, [
    currentMode,
    questionsConfig.questions,
    questionsConfig.questionTypes,
    questionsConfig.totalQuestions,
    onValidationChange,
    getManualQuestionCounts,
  ]);

  return (
    <div className="space-y-6">
      {/* Question Generation Mode Selection */}
      <FormField
        control={form.control}
        name="questionsConfig.mode"
        render={({ field }) => (
          <FormItem className="space-y-4">
            <div>
              <Label className="text-lg font-medium">Question Generation Method</Label>
              <p className="text-sm text-muted-foreground mt-1">
                Choose how you want to create interview questions
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {questionModes.map((mode) => (
                <div
                  key={mode.value}
                  className={cn(
                    'relative border rounded-lg p-4 cursor-pointer transition-all',
                    field.value === mode.value
                      ? 'border-primary bg-primary/5 ring-2 ring-primary/20'
                      : 'border-muted hover:border-primary/50'
                  )}
                  onClick={() => handleModeChange(mode.value as 'manual' | 'ai-mode')}
                >
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <div
                        className={cn(
                          'w-4 h-4 rounded-full border-2',
                          field.value === mode.value
                            ? 'border-primary bg-primary'
                            : 'border-muted-foreground'
                        )}
                      >
                        {field.value === mode.value && (
                          <Check className="w-2 h-2 text-primary-foreground ml-0.5 mt-0.5" />
                        )}
                      </div>
                      <h3 className="font-medium">{mode.label}</h3>
                    </div>
                    <p className="text-sm text-muted-foreground">{mode.description}</p>
                  </div>
                </div>
              ))}
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Question Categories */}
      <FormField
        control={form.control}
        name="questionsConfig.questionTypes"
        render={({ field }) => {
          const selectedIndustry = form.watch('industry');
          const availableQuestionTypes = getQuestionTypesForIndustry();

          return (
            <FormItem className="space-y-4">
              <div>
                <Label className="text-lg font-medium">Question Categories</Label>
                <p className="text-sm text-muted-foreground mt-1">
                  Select question categories and set the number of questions for each
                </p>
              </div>

              {!selectedIndustry && (
                <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-sm text-yellow-800">
                    Please select an industry in Step 1 to see relevant question categories.
                  </p>
                </div>
              )}

              {selectedIndustry && (
                <div className="grid grid-cols-1 gap-4">
                  {availableQuestionTypes.map((questionType) => {
                    const isSelected = field.value?.includes(questionType.value) || false;
                    const categoryConfig = questionsConfig.categoryConfigs?.find(
                      (config) => config.type === questionType.value
                    );
                    const questionCount = categoryConfig?.numberOfQuestions || 0;

                    return (
                      <div
                        key={questionType.value}
                        className={cn(
                          'flex items-center justify-between p-4 border rounded-lg transition-all',
                          isSelected ? 'border-primary bg-primary/5' : 'border-border'
                        )}
                      >
                        <div className="flex items-start space-x-3 flex-1">
                          <Checkbox
                            id={questionType.value}
                            checked={isSelected}
                            onCheckedChange={(checked) => {
                              const currentTypes = field.value || [];
                              if (checked) {
                                const newTypes = [...currentTypes, questionType.value];
                                field.onChange(newTypes);
                                updateCategoryConfigs(newTypes);
                              } else {
                                const newTypes = currentTypes.filter(
                                  (type) => type !== questionType.value
                                );
                                field.onChange(newTypes);
                                updateCategoryConfigs(newTypes);
                              }
                            }}
                          />
                          <div className="flex-1">
                            <Label
                              htmlFor={questionType.value}
                              className="text-sm font-medium cursor-pointer"
                            >
                              {questionType.label}
                            </Label>
                            <p className="text-xs text-muted-foreground mt-1">
                              {questionType.description}
                            </p>
                          </div>
                        </div>

                        {isSelected && (
                          <div className="flex items-center space-x-2 ml-4">
                            <Label className="text-xs text-muted-foreground">Questions:</Label>
                            <div className="flex items-center space-x-1">
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() =>
                                  updateCategoryQuestionCount(questionType.value, questionCount - 1)
                                }
                                disabled={questionCount <= 0}
                              >
                                -
                              </Button>
                              <span className="w-8 text-center text-sm font-medium">
                                {questionCount}
                              </span>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() =>
                                  updateCategoryQuestionCount(questionType.value, questionCount + 1)
                                }
                                disabled={questionCount >= 20}
                              >
                                +
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
              <FormMessage />
            </FormItem>
          );
        }}
      />

      {/* Total Questions Display */}
      {questionsConfig.categoryConfigs &&
        questionsConfig.categoryConfigs.length > 0 &&
        currentMode !== 'manual' && (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium text-blue-900">Total Questions</Label>
                <p className="text-xs text-blue-800 mt-1">
                  AI will handle question generation and interview management
                </p>
              </div>
              <div className="text-lg font-bold text-blue-900">
                {questionsConfig.totalQuestions}
              </div>
            </div>
          </div>
        )}

      {/* AI Mode Info */}
      {currentMode === 'ai-mode' && (
        <div className="p-4 border rounded-lg bg-blue-50/50">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 mt-1">
              <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                <Check className="w-3 h-3 text-white" />
              </div>
            </div>
            <div>
              <h4 className="font-medium text-blue-900">AI Mode</h4>
              <p className="text-sm text-blue-800 mt-1">
                In this mode, AI will handle the entire interview process:
              </p>
              <ul className="text-sm text-blue-800 mt-2 ml-4 list-disc">
                <li>Dynamic question generation based on job requirements and industry</li>
                <li>Real-time analysis of candidate responses</li>
                <li>Follow-up questions based on candidate answers</li>
                <li>Automatic interview flow management</li>
              </ul>
              <p className="text-sm text-blue-800 mt-2">
                The AI will conduct approximately{' '}
                <span className="font-medium">{questionsConfig.totalQuestions}</span> questions
                distributed across the selected categories based on your configuration.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* AI Mode Completion Indicator */}
      {currentMode === 'ai-mode' &&
        questionsConfig.questionTypes &&
        questionsConfig.questionTypes.length > 0 &&
        questionsConfig.totalQuestions > 0 && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <Check className="h-4 w-4 text-green-600" />
              <div>
                <Label className="text-sm font-medium text-green-900">
                  AI Mode Configuration Complete!
                </Label>
                <p className="text-xs text-green-800 mt-1">
                  {questionsConfig.totalQuestions} questions across{' '}
                  {questionsConfig.questionTypes.length} categories. You can now proceed to the next
                  step.
                </p>
              </div>
            </div>
          </div>
        )}

      {/* Manual Question Entry */}
      {currentMode === 'manual' && (
        <div className="space-y-4">
          <div>
            <Label className="text-base font-medium">Manual Question Entry</Label>
            <p className="text-sm text-muted-foreground mt-1">
              Create custom questions manually with AI assistance
            </p>
          </div>

          {/* Question Count Tracking for Manual Mode */}
          {questionsConfig.totalQuestions > 0 && (
            <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-sm font-medium text-amber-900">
                    Question Entry Progress
                  </Label>
                  <p className="text-xs text-amber-800 mt-1">
                    You need to create {getManualQuestionCounts().totalRequested} questions as per
                    your category configuration
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-amber-900">
                    {getManualQuestionCounts().totalEntered} /{' '}
                    {getManualQuestionCounts().totalRequested}
                  </div>
                  <div className="text-xs text-amber-800">
                    {getManualQuestionCounts().remaining > 0
                      ? `${getManualQuestionCounts().remaining} remaining`
                      : 'Complete!'}
                  </div>
                </div>
              </div>
              {getManualQuestionCounts().remaining > 0 && (
                <div className="mt-2 text-xs text-amber-800 font-medium">
                  ⚠️ Please add {getManualQuestionCounts().remaining} more question(s) to meet your
                  requirements
                </div>
              )}
            </div>
          )}

          {getManualQuestionCounts().isComplete && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <Check className="h-4 w-4 text-green-600" />
                <div>
                  <Label className="text-sm font-medium text-green-900">
                    Questions Configuration Complete!
                  </Label>
                  <p className="text-xs text-green-800 mt-1">
                    You can now proceed to the next step or review your job posting.
                  </p>
                </div>
              </div>
            </div>
          )}

          {questionsConfig.totalQuestions === 0 && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-sm text-yellow-800">
                Please select question categories above and set the number of questions to start
                creating manual questions.
              </p>
            </div>
          )}

          {/* AI Generation Option for Manual Mode */}
          {questionsConfig.totalQuestions > 0 && questionsConfig.questionTypes.length > 0 && (
            <div className="space-y-4 p-4 border rounded-lg bg-blue-50/50">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-base font-medium">AI Question Suggestions</Label>
                  <p className="text-sm text-muted-foreground mt-1">
                    Generate AI questions that you can review, edit, and customize
                  </p>
                </div>
                <AlertDialog open={showAIConfirmDialog} onOpenChange={setShowAIConfirmDialog}>
                  <AlertDialogTrigger asChild>
                    <Button
                      type="button"
                      onClick={handleGenerateAIQuestions}
                      variant="outline"
                      className="border-blue-300 text-blue-700 hover:bg-blue-100"
                    >
                      Generate AI Suggestions
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Replace Existing Questions?</AlertDialogTitle>
                      <AlertDialogDescription>
                        You already have {(questionsConfig.questions || []).length} questions
                        created. Generating new AI questions will replace all existing questions.
                        This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleConfirmAIGeneration}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        Replace Questions
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>

              <div className="text-sm text-muted-foreground">
                <p>
                  Will generate{' '}
                  <span className="font-medium">{questionsConfig.totalQuestions}</span> AI question
                  suggestions that you can edit and customize.
                </p>
              </div>
            </div>
          )}

          {questionsConfig.totalQuestions > 0 && (
            <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="questionType">Question Type</Label>
                  <Select value={newQuestionType} onValueChange={setNewQuestionType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {getSelectedQuestionTypes().length > 0 ? (
                        getSelectedQuestionTypes().map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-categories" disabled>
                          No question categories selected
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="questionText">Question Text *</Label>
                <div className="flex items-center space-x-2">
                  <Textarea
                    id="questionText"
                    placeholder="Enter your question text..."
                    value={newQuestionText}
                    onChange={(e) => setNewQuestionText(e.target.value)}
                    className="min-h-[80px]"
                  />
                  <Button
                    type="button"
                    onClick={handleAddManualQuestion}
                    disabled={
                      !newQuestionText.trim() ||
                      getManualQuestionCounts().totalEntered >=
                        getManualQuestionCounts().totalRequested
                    }
                  >
                    {getManualQuestionCounts().totalEntered >=
                    getManualQuestionCounts().totalRequested
                      ? 'Limit Reached'
                      : 'Add Question'}
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">* Question text is required</p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Display Created Questions */}
      {(questionsConfig.questions?.length ?? 0) > 0 && (
        <div className="space-y-4">
          <div>
            <Label className="text-base font-medium">Created Questions</Label>
            <p className="text-sm text-muted-foreground mt-1">
              Review and manage your interview questions
            </p>
          </div>

          <div className="space-y-3">
            {questionsConfig.questions?.map((question, index) => (
              <Card key={question.id}>
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="text-xs">
                        {editingQuestionId === question.id
                          ? getQuestionTypeLabel(editingQuestionType)
                          : getQuestionTypeLabel(question.type)}
                      </Badge>
                      {question.isAIGenerated && (
                        <Badge variant="secondary" className="text-xs">
                          AI Generated
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-1">
                      {editingQuestionId === question.id ? (
                        <>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={handleSaveEdit}
                            className="text-green-600 hover:text-green-700"
                          >
                            <Save className="h-4 w-4" />
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={handleCancelEdit}
                            className="text-gray-600 hover:text-gray-700"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditQuestion(question)}
                            className="text-blue-600 hover:text-blue-700"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveQuestion(question.id)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {editingQuestionId === question.id ? (
                    <div className="space-y-3">
                      <div className="space-y-2">
                        <Label className="text-xs text-muted-foreground">Question Type</Label>
                        <Select value={editingQuestionType} onValueChange={setEditingQuestionType}>
                          <SelectTrigger className="h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {getSelectedQuestionTypes().length > 0 ? (
                              getSelectedQuestionTypes().map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  {type.label}
                                </SelectItem>
                              ))
                            ) : (
                              <SelectItem value="no-categories" disabled>
                                No question categories selected
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="space-y-2">
                        <Label className="text-xs text-muted-foreground">Question Text</Label>
                        <Textarea
                          value={editingQuestionText}
                          onChange={(e) => setEditingQuestionText(e.target.value)}
                          className="min-h-[60px] text-sm"
                          placeholder="Enter question text..."
                        />
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm leading-relaxed">
                      <span className="font-medium">Q{index + 1}:</span> {question.question}
                    </p>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
