'use client';

import { format } from 'date-fns';
import { Calendar, Clock, FileText, HelpCircle, MapPin, Users } from 'lucide-react';
import { type UseFormReturn } from 'react-hook-form';

import { type FormValues, type Organization } from '@/@types/job';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import DifficultyLevelBadge from '@/components/ui/difficulty-level-badge';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { currencies } from '@/data/currencies';

import { jobStatuses, questionModes } from './data/job-form-constants';

interface ReviewStepProps {
  form: UseFormReturn<FormValues>;
  organizations: Organization[];
}

export function ReviewStep({ form, organizations }: ReviewStepProps) {
  const formData = form.getValues();
  const selectedOrganization = organizations?.find((org) => org.id === formData.organizationId);
  const selectedCurrency = currencies.find((c) => c.value === formData.currency);
  const selectedStatus = jobStatuses.find((s) => s.value === formData.status);
  const selectedQuestionMode = questionModes.find((m) => m.value === formData.questionsConfig.mode);

  return (
    <ScrollArea className="h-[600px] pr-4">
      <div className="space-y-6">
        {/* Basic Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Basic Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Job Title</Label>
                <p className="mt-1 font-medium">{formData.title}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Organization</Label>
                <p className="mt-1">{selectedOrganization?.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Industry</Label>
                <p className="mt-1 capitalize">{formData.industry}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                <Badge variant={formData.status === 'published' ? 'default' : 'secondary'}>
                  {selectedStatus?.label}
                </Badge>
              </div>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span>{formData.location}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>Expires: {format(formData.expiryDate, 'PPP')}</span>
              </div>
            </div>

            {formData.salary && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Salary</Label>
                <p className="mt-1">
                  {selectedCurrency?.symbol}
                  {formData.salary} {selectedCurrency?.value}
                </p>
              </div>
            )}

            <div>
              <Label className="text-sm font-medium text-muted-foreground">Required Skills</Label>
              <div className="mt-2 flex flex-wrap gap-2">
                {formData.skills.map((skill) => (
                  <Badge key={skill} variant="outline">
                    {skill}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Job Description */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Job Description
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Description</Label>
              <div className="mt-2 p-3 bg-muted/50 rounded-md">
                <p className="text-sm whitespace-pre-wrap">{formData.description}</p>
              </div>
            </div>

            {formData.requirements && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Requirements</Label>
                <div className="mt-2 p-3 bg-muted/50 rounded-md">
                  <p className="text-sm whitespace-pre-wrap">{formData.requirements}</p>
                </div>
              </div>
            )}

            {formData.benefits && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Benefits</Label>
                <div className="mt-2 p-3 bg-muted/50 rounded-md">
                  <p className="text-sm whitespace-pre-wrap">{formData.benefits}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Interview Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Interview Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Duration</Label>
              <p className="mt-1">{formData.interviewConfig.duration} minutes</p>
            </div>

            <div>
              <Label className="text-sm font-medium text-muted-foreground">Difficulty Level</Label>
              <div className="mt-2 flex items-center gap-3">
                <DifficultyLevelBadge level={formData.interviewConfig.difficultyLevel} size="lg" />
                <div className="flex flex-col">
                  <span className="text-sm font-medium">Interview Complexity</span>
                  <span className="text-xs text-muted-foreground">
                    Questions will be tailored to this difficulty level
                  </span>
                </div>
              </div>
            </div>

            {formData.interviewConfig.instructions && (
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Instructions</Label>
                <div className="mt-2 p-3 bg-muted/50 rounded-md">
                  <p className="text-sm whitespace-pre-wrap">
                    {formData.interviewConfig.instructions}
                  </p>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Screen Monitoring
                </Label>
                <div className="mt-1 space-y-1">
                  <p className="font-medium">
                    {formData.interviewConfig.screenMonitoring ? 'Enabled' : 'Disabled'}
                  </p>
                  {formData.interviewConfig.screenMonitoring && (
                    <>
                      <p className="text-sm text-muted-foreground">
                        Mode: {formData.interviewConfig.screenMonitoringMode}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Interval: Every {formData.interviewConfig.screenMonitoringInterval} seconds
                      </p>
                    </>
                  )}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">
                  Camera Monitoring
                </Label>
                <div className="mt-1 space-y-1">
                  <p className="font-medium">
                    {formData.interviewConfig.cameraMonitoring ? 'Enabled' : 'Disabled'}
                  </p>
                  {formData.interviewConfig.cameraMonitoring && (
                    <>
                      <p className="text-sm text-muted-foreground">
                        Mode: {formData.interviewConfig.cameraMonitoringMode}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Interval: Every {formData.interviewConfig.cameraMonitoringInterval} seconds
                      </p>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Questions Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5" />
              Questions Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Question Mode</Label>
                <p className="mt-1">{selectedQuestionMode?.label}</p>
                <p className="text-sm text-muted-foreground">{selectedQuestionMode?.description}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Total Questions</Label>
                <p className="mt-1">{formData.questionsConfig.totalQuestions}</p>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium text-muted-foreground">Question Types</Label>
              <div className="mt-2 flex flex-wrap gap-2">
                {formData.questionsConfig.questionTypes.map((type) => (
                  <Badge key={type} variant="outline">
                    {type.replace(/-/g, ' ')}
                  </Badge>
                ))}
              </div>
            </div>

            {formData.questionsConfig.questions &&
              formData.questionsConfig.questions.length > 0 && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Custom Questions
                  </Label>
                  <div className="mt-2 space-y-2">
                    {formData.questionsConfig.questions.map((question, index) => (
                      <div key={question.id} className="p-3 bg-muted/50 rounded-md">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge variant="secondary" className="text-xs">
                            {question.type.replace(/-/g, ' ')}
                          </Badge>
                          {question.isAIGenerated && (
                            <Badge variant="outline" className="text-xs">
                              AI Generated
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm">
                          {index + 1}. {question.question}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

            {formData.questionsConfig.categoryConfigs &&
              formData.questionsConfig.categoryConfigs.length > 0 && (
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">
                    Questions per Category
                  </Label>
                  <div className="mt-2 space-y-2">
                    {formData.questionsConfig.categoryConfigs.map((config) => (
                      <div
                        key={config.type}
                        className="flex justify-between items-center p-2 bg-muted/50 rounded-md"
                      >
                        <span className="text-sm font-medium">
                          {config.type.replace(/-/g, ' ')}
                        </span>
                        <Badge variant="outline">{config.numberOfQuestions} questions</Badge>
                      </div>
                    ))}
                  </div>
                </div>
              )}
          </CardContent>
        </Card>
      </div>
    </ScrollArea>
  );
}
