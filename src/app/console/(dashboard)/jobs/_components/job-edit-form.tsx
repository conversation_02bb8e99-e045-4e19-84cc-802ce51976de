'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Save } from 'lucide-react';
import { useEffect, useState } from 'react';
import { type Resolver, useForm } from 'react-hook-form';
import { toast } from 'sonner';

import {
  type FormValues,
  type JobForComponents,
  jobFormSchema as formSchema,
  type LegacyQuestionsConfig,
} from '@/@types/job';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { TabsContent } from '@/components/ui/tabs';
import { useUpdateJob } from '@/hooks/use-jobs';

import { BasicDetailsStep } from './basic-details-step';
import { DescriptionStep } from './description-step';
import { InterviewStep } from './interview-step';
import { JobSettingsTab } from './job-settings-tab';
import { QuestionsStep } from './questions-step';

interface Job {
  id: string;
  title: string;
  organizationId: string;
  organizationName?: string;
  industry: string;
  location: string;
  salary?: string;
  currency?: string;
  skills: string[];
  description: string;
  requirements?: string;
  benefits?: string;
  status: 'draft' | 'published' | 'expired' | 'deleted';
  interviewConfig: {
    duration: number;
    instructions?: string;
    difficultyLevel: 'easy' | 'normal' | 'hard' | 'expert' | 'advanced';
    screenMonitoring: boolean;
    screenMonitoringMode: 'photo' | 'video';
    screenMonitoringInterval?: 30 | 60;
    cameraMonitoring: boolean;
    cameraMonitoringMode: 'photo' | 'video';
    cameraMonitoringInterval?: 30 | 60;
  };
  questionsConfig: LegacyQuestionsConfig | FormValues['questionsConfig']; // Allow for legacy structures
  expiryDate: Date;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  deletedBy?: string;
}

interface JobEditFormProps {
  job: JobForComponents;
  activeTab: string;
}

// Migration utility to convert old questionsConfig to new format
const migrateQuestionsConfig = (
  questionsConfig: LegacyQuestionsConfig | FormValues['questionsConfig']
): FormValues['questionsConfig'] => {
  // If it already has the new structure, return as is
  if (
    questionsConfig &&
    'totalQuestions' in questionsConfig &&
    questionsConfig.totalQuestions !== undefined &&
    'categoryConfigs' in questionsConfig &&
    questionsConfig.categoryConfigs !== undefined
  ) {
    return questionsConfig as FormValues['questionsConfig'];
  }

  // If it has the old structure with numberOfQuestions, convert it
  if (
    questionsConfig &&
    'numberOfQuestions' in questionsConfig &&
    questionsConfig.numberOfQuestions !== undefined
  ) {
    const questionTypes = questionsConfig.questionTypes || ['behavioral'];
    const questionsPerCategory =
      questionTypes.length > 0
        ? Math.ceil(questionsConfig.numberOfQuestions / questionTypes.length)
        : 1;

    const categoryConfigs = questionTypes.map((type: string) => ({
      type,
      numberOfQuestions: questionsPerCategory,
    }));

    return {
      mode: questionsConfig.mode || 'manual',
      totalQuestions: questionsConfig.numberOfQuestions || 5,
      categoryConfigs,
      questionTypes: questionsConfig.questionTypes || ['behavioral'],
      questions: questionsConfig.questions || [],
    };
  }

  // Handle cases where questionsConfig exists but is incomplete
  if (questionsConfig && typeof questionsConfig === 'object') {
    const questionTypes = questionsConfig.questionTypes || ['behavioral'];
    const totalQuestions = questionsConfig.totalQuestions || 5;

    // Generate default categoryConfigs if missing
    let categoryConfigs = questionsConfig.categoryConfigs || [];
    if (categoryConfigs.length === 0 && questionTypes.length > 0) {
      const questionsPerCategory = Math.ceil(totalQuestions / questionTypes.length);
      categoryConfigs = questionTypes.map((type: string) => ({
        type,
        numberOfQuestions: questionsPerCategory,
      }));
    }

    return {
      mode: questionsConfig.mode || 'manual',
      totalQuestions,
      categoryConfigs,
      questionTypes,
      questions: questionsConfig.questions || [],
    };
  }

  // Default structure if nothing exists or questionsConfig is null/undefined
  return {
    mode: 'manual',
    totalQuestions: 5,
    categoryConfigs: [
      {
        type: 'behavioral',
        numberOfQuestions: 5,
      },
    ],
    questionTypes: ['behavioral'],
    questions: [],
  };
};

export function JobEditForm({ job }: JobEditFormProps) {
  const updateJobMutation = useUpdateJob();
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize form with job data using migrated questionsConfig
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema) as Resolver<FormValues>,
    defaultValues: {
      title: job.title || '',
      organizationId: job.organizationId || '',
      industry: job.industry || '',
      salary: job.salary || '',
      currency: job.currency || '',
      location: job.location || '',
      skills: job.skills || [],
      status: job.status === 'deleted' ? 'draft' : job.status || 'draft',
      description: job.description || '',
      requirements: job.requirements || '',
      benefits: job.benefits || '',
      interviewConfig: {
        duration: job.interviewConfig?.duration || 30,
        instructions: job.interviewConfig?.instructions || '',
        difficultyLevel: job.interviewConfig?.difficultyLevel || 'normal',
        screenMonitoring: job.interviewConfig?.screenMonitoring || false,
        screenMonitoringMode: job.interviewConfig?.screenMonitoringMode || 'photo',
        screenMonitoringInterval: job.interviewConfig?.screenMonitoringInterval || 30,
        cameraMonitoring: job.interviewConfig?.cameraMonitoring || false,
        cameraMonitoringMode: job.interviewConfig?.cameraMonitoringMode || 'photo',
        cameraMonitoringInterval: job.interviewConfig?.cameraMonitoringInterval || 30,
      },
      questionsConfig: migrateQuestionsConfig(job.questionsConfig),
      expiryDate: job.expiryDate
        ? new Date(job.expiryDate)
        : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    },
  });

  // Watch for changes
  useEffect(() => {
    const subscription = form.watch(() => {
      setHasChanges(form.formState.isDirty);
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Also watch formState directly for better reactivity
  useEffect(() => {
    setHasChanges(form.formState.isDirty);
  }, [form.formState.isDirty]);

  // Reset form when job data changes
  useEffect(() => {
    if (job) {
      form.reset({
        title: job.title || '',
        organizationId: job.organizationId || '',
        industry: job.industry || '',
        salary: job.salary || '',
        currency: job.currency || '',
        location: job.location || '',
        skills: job.skills || [],
        status: job.status === 'deleted' ? 'draft' : job.status || 'draft',
        description: job.description || '',
        requirements: job.requirements || '',
        benefits: job.benefits || '',
        interviewConfig: {
          duration: job.interviewConfig?.duration || 30,
          instructions: job.interviewConfig?.instructions || '',
          difficultyLevel: job.interviewConfig?.difficultyLevel || 'normal',
          screenMonitoring: job.interviewConfig?.screenMonitoring || false,
          screenMonitoringMode: job.interviewConfig?.screenMonitoringMode || 'photo',
          screenMonitoringInterval: job.interviewConfig?.screenMonitoringInterval || 30,
          cameraMonitoring: job.interviewConfig?.cameraMonitoring || false,
          cameraMonitoringMode: job.interviewConfig?.cameraMonitoringMode || 'photo',
          cameraMonitoringInterval: job.interviewConfig?.cameraMonitoringInterval || 30,
        },
        questionsConfig: migrateQuestionsConfig(
          job.questionsConfig
        ) as FormValues['questionsConfig'],
        expiryDate: job.expiryDate
          ? new Date(job.expiryDate)
          : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      });
      setHasChanges(false);
    }
  }, [job, form]);

  const onSubmit = async (data: FormValues) => {
    try {
      await updateJobMutation.mutateAsync({
        id: job.id,
        ...data,
      });
      toast.success('Job updated successfully');

      // Reset form with the submitted data and mark as pristine
      form.reset(data, {
        keepDefaultValues: false,
        keepValues: false,
        keepDirty: false,
        keepTouched: false,
      });

      // Force hasChanges to false
      setHasChanges(false);

      // Trigger a re-render by updating form state
      setTimeout(() => {
        setHasChanges(form.formState.isDirty);
      }, 100);
    } catch (error) {
      console.error('Error updating job:', error);
      toast.error('Failed to update job. Please try again.');
    }
  };

  // Create a migrated job object with proper questionsConfig structure for JobSettingsTab
  const migratedJob = {
    ...job,
    questionsConfig: migrateQuestionsConfig(job.questionsConfig),
  } as Job & { questionsConfig: FormValues['questionsConfig'] };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <TabsContent value="basic" className="mt-0">
          <BasicDetailsStep form={form} />
        </TabsContent>

        <TabsContent value="description" className="mt-0">
          <DescriptionStep form={form} />
        </TabsContent>

        <TabsContent value="interview" className="mt-0">
          <InterviewStep form={form} />
        </TabsContent>

        <TabsContent value="questions" className="mt-0">
          <QuestionsStep form={form} />
        </TabsContent>

        <TabsContent value="settings" className="mt-0">
          <JobSettingsTab job={migratedJob} form={form} />
        </TabsContent>

        {/* Save Button - Fixed at bottom */}
        {hasChanges && (
          <div className="sticky bottom-0 bg-background border-t p-4 flex justify-end">
            <Button type="submit" disabled={updateJobMutation.isPending} className="min-w-[120px]">
              {updateJobMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        )}
      </form>
    </Form>
  );
}
