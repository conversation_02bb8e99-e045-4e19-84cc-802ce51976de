import { UseFormReturn } from 'react-hook-form';
import { toast } from 'sonner';

import type { FormValues } from '@/@types/job';
import { Button } from '@/components/ui/button';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';

import { generateJobDescription } from './utils/ai-helpers';

interface DescriptionStepProps {
  form: UseFormReturn<FormValues>;
  organizationsData?: {
    data: Array<{
      id: string;
      name: string;
      slug: string;
    }>;
  };
}

export function DescriptionStep({ form, organizationsData }: DescriptionStepProps) {
  const handleGenerateDescription = async () => {
    const { title, organizationId, skills } = form.getValues();
    const selectedOrg = organizationsData?.data.find((org) => org.id === organizationId);
    const organizationName = selectedOrg?.name || 'Our company';

    if (!title || !organizationId || !skills.length) {
      toast.error(
        'Please fill in job title, organization name, and select at least one skill first'
      );
      return;
    }

    try {
      const description = await generateJobDescription(title, organizationName, skills);
      form.setValue('description', description);
      toast.success('Job description generated successfully');
    } catch {
      toast.error('Failed to generate job description');
    }
  };

  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <div className="flex items-center justify-between">
              <FormLabel>Job Description *</FormLabel>
              <Button type="button" variant="outline" size="sm" onClick={handleGenerateDescription}>
                Generate with AI
              </Button>
            </div>
            <FormControl>
              <Textarea
                placeholder="Describe the role, responsibilities, and what you're looking for in a candidate"
                className="min-h-[200px]"
                {...field}
              />
            </FormControl>
            <FormDescription>
              Provide a detailed description of the job role, responsibilities, and expectations.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="requirements"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Requirements (Optional)</FormLabel>
            <FormControl>
              <Textarea
                placeholder="List the required qualifications, experience, and skills"
                className="min-h-[120px]"
                {...field}
              />
            </FormControl>
            <FormDescription>
              Specify the minimum qualifications and requirements for this position.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="benefits"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Benefits (Optional)</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Describe the benefits and perks offered"
                className="min-h-[120px]"
                {...field}
              />
            </FormControl>
            <FormDescription>
              Highlight the benefits, perks, and compensation package.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
