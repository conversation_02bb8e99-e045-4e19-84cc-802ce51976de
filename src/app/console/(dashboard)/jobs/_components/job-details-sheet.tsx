'use client';

import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

import type { JobForComponents as Job } from '@/@types/job';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { getSkillLabel } from '@/data/skills';

// Helper function to safely get recruiter info
const getRecruiterInfo = (recruiter: unknown) => {
  if (typeof recruiter === 'string') {
    return { name: 'Unknown', email: 'Unknown' };
  }
  if (recruiter && typeof recruiter === 'object') {
    const recruiterObj = recruiter as { name?: string; email?: string; _id?: unknown };
    return {
      name: recruiterObj.name || 'Unknown',
      email: recruiterObj.email || 'Unknown',
    };
  }
  return { name: 'Unknown', email: 'Unknown' };
};

interface JobDetailsSheetProps {
  job: Job | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function JobDetailsSheet({ job, open, onOpenChange }: JobDetailsSheetProps) {
  if (!job) return null;

  const isExpired = new Date(job.expiryDate) < new Date();

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-full sm:max-w-2xl overflow-y-auto">
        <SheetHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <SheetTitle className="text-xl">{job.title}</SheetTitle>
              <SheetDescription className="text-base">
                {job.organizationName || 'Unknown Organization'} • {job.location}
              </SheetDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Badge
                variant={
                  job.status === 'published'
                    ? 'default'
                    : job.status === 'draft'
                      ? 'secondary'
                      : 'destructive'
                }
              >
                {job.status === 'published' ? 'Published' : job.status}
              </Badge>
              {isExpired && <Badge variant="destructive">Expired</Badge>}
            </div>
          </div>
        </SheetHeader>

        <div className="space-y-6 mt-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Job Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Salary</Label>
                  <p className="text-sm">{job.salary || 'Not specified'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                  <p className="text-sm capitalize">{job.status}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Expiry Date</Label>
                  <div className="flex items-center space-x-1">
                    <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                    <p className="text-sm">{format(new Date(job.expiryDate), 'PPP')}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Skills */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Required Skills</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {job.skills.map((skill) => (
                  <Badge key={skill} variant="outline">
                    {getSkillLabel(skill)}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Description */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Description</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose prose-sm max-w-none">
                <p className="whitespace-pre-wrap text-sm leading-relaxed">{job.description}</p>
              </div>
            </CardContent>
          </Card>

          {/* Requirements */}
          {job.requirements && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Requirements</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none">
                  <p className="whitespace-pre-wrap text-sm leading-relaxed">{job.requirements}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Benefits */}
          {job.benefits && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Benefits</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none">
                  <p className="whitespace-pre-wrap text-sm leading-relaxed">{job.benefits}</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recruiter */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recruiter</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Name</Label>
                  <p className="text-sm">{getRecruiterInfo(job.recruiter).name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Email</Label>
                  <p className="text-sm">{getRecruiterInfo(job.recruiter).email}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Timestamps */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Timestamps</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Created</Label>
                  <p className="text-sm">{format(new Date(job.createdAt), 'PPP p')}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Last Updated</Label>
                  <p className="text-sm">{format(new Date(job.updatedAt), 'PPP p')}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </SheetContent>
    </Sheet>
  );
}
