'use client';

import { <PERSON>, <PERSON>, User<PERSON>he<PERSON>, UserX } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface JobApplicationsTabProps {
  jobId: string;
}

// Mock data for applications - replace with actual API call
const mockApplications = [
  {
    id: '1',
    candidateName: '<PERSON>',
    candidateEmail: '<EMAIL>',
    status: 'pending',
    appliedAt: new Date('2024-12-01'),
    score: 85,
    resumeUrl: '#',
  },
  {
    id: '2',
    candidateName: '<PERSON>',
    candidateEmail: '<EMAIL>',
    status: 'shortlisted',
    appliedAt: new Date('2024-12-02'),
    score: 92,
    resumeUrl: '#',
  },
  {
    id: '3',
    candidateName: 'Mike <PERSON>',
    candidateEmail: '<EMAIL>',
    status: 'rejected',
    appliedAt: new Date('2024-12-03'),
    score: 68,
    resumeUrl: '#',
  },
];

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  shortlisted: 'bg-green-100 text-green-800',
  rejected: 'bg-red-100 text-red-800',
  interviewed: 'bg-blue-100 text-blue-800',
};

export function JobApplicationsTab({ jobId: _jobId }: JobApplicationsTabProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Filter applications based on search and status
  const filteredApplications = mockApplications.filter((application) => {
    const matchesSearch =
      application.candidateName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      application.candidateEmail.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || application.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-2xl font-bold">{mockApplications.length}</div>
          <div className="text-sm text-muted-foreground">Total Applications</div>
        </Card>
        <Card className="p-4">
          <div className="text-2xl font-bold text-yellow-600">
            {mockApplications.filter((app) => app.status === 'pending').length}
          </div>
          <div className="text-sm text-muted-foreground">Pending Review</div>
        </Card>
        <Card className="p-4">
          <div className="text-2xl font-bold text-green-600">
            {mockApplications.filter((app) => app.status === 'shortlisted').length}
          </div>
          <div className="text-sm text-muted-foreground">Shortlisted</div>
        </Card>
        <Card className="p-4">
          <div className="text-2xl font-bold text-blue-600">
            {mockApplications.filter((app) => app.status === 'interviewed').length}
          </div>
          <div className="text-sm text-muted-foreground">Interviewed</div>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Filter Applications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Label htmlFor="search">Search Candidates</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <Label htmlFor="status">Status</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="shortlisted">Shortlisted</SelectItem>
                  <SelectItem value="interviewed">Interviewed</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Applications Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Candidate Applications</CardTitle>
          <CardDescription>Review and manage applications for this position</CardDescription>
        </CardHeader>
        <CardContent>
          {filteredApplications.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                {mockApplications.length === 0
                  ? 'No applications yet'
                  : 'No applications match your filters'}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[200px]">Candidate</TableHead>
                    <TableHead className="min-w-[120px]">Status</TableHead>
                    <TableHead className="min-w-[100px]">Score</TableHead>
                    <TableHead className="min-w-[120px]">Applied Date</TableHead>
                    <TableHead className="min-w-[200px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredApplications.map((application) => (
                    <TableRow key={application.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{application.candidateName}</p>
                          <p className="text-sm text-muted-foreground">
                            {application.candidateEmail}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="secondary"
                          className={statusColors[application.status as keyof typeof statusColors]}
                        >
                          {application.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-primary h-2 rounded-full"
                              style={{ width: `${application.score}%` }}
                            />
                          </div>
                          <span className="text-sm font-medium">{application.score}%</span>
                        </div>
                      </TableCell>
                      <TableCell>{application.appliedAt.toLocaleDateString()}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Button>
                          <Button size="sm" variant="outline">
                            <UserCheck className="h-4 w-4 mr-1" />
                            Shortlist
                          </Button>
                          <Button size="sm" variant="outline">
                            <UserX className="h-4 w-4 mr-1" />
                            Reject
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
