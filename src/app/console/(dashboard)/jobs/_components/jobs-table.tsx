'use client';

import { format } from 'date-fns';
import { CalendarIcon, Eye, MapPin, MoreHorizontal, Search, Settings, Users } from 'lucide-react';
import { useState } from 'react';

import type { JobForComponents as Job } from '@/@types/job';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import DifficultyLevelBadge from '@/components/ui/difficulty-level-badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { TableWrapper } from '@/components/ui/table-wrapper';
import { getSkillLabel } from '@/data/skills';
import { useJobs } from '@/hooks/use-jobs';
import { cn } from '@/lib/utils';

import JobDetailsSheet from './job-details-sheet';

// Helper function to transform job data for components
const transformJobsForComponents = (jobs: Record<string, unknown>[]): Job[] => {
  return jobs.map((job) => ({
    ...job,
    organizationId:
      typeof job.organizationId === 'string'
        ? job.organizationId
        : job.organizationId?.toString() || '',
    recruiter: typeof job.recruiter === 'string' ? job.recruiter : job.recruiter?.toString() || '',
  })) as Job[];
};

interface JobsTableProps {
  className?: string;
  organizationId?: string; // Optional prop to filter by organization
}

export function JobsTable({ className, organizationId }: JobsTableProps) {
  const [search, setSearch] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | undefined>(undefined);
  const [page] = useState(1);
  const [limit] = useState(10);

  // Modal states
  const [detailsSheetOpen, setDetailsSheetOpen] = useState(false);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);

  // Convert status filter to isActive for API compatibility
  const isActiveFilter =
    statusFilter === 'all'
      ? undefined
      : statusFilter === 'published'
        ? true
        : statusFilter === 'draft'
          ? false
          : undefined;

  // Hooks
  const { data: jobsData, error } = useJobs({
    search,
    isActive: isActiveFilter,
    organizationId,
    page,
    limit,
  });

  const jobs = transformJobsForComponents(
    (jobsData?.data || []) as unknown as Record<string, unknown>[]
  );

  const handleViewApplications = (job: Job) => {
    // Navigate to the applications page
    window.location.href = `/console/jobs/${job.id}/applications`;
  };

  const handleManageJob = (job: Job) => {
    // Navigate to the job management/edit page
    window.location.href = `/console/jobs/${job.id}?tab=basic`;
  };

  const handleViewJob = (job: Job) => {
    setSelectedJob(job);
    setDetailsSheetOpen(true);
  };

  const columns = [
    {
      accessorKey: 'title',
      header: 'Job Title',
      cell: ({ row }: { row: { original: Job } }) => {
        const job = row.original;
        const isExpired = new Date(job.expiryDate) < new Date();
        return (
          <div className="space-y-1">
            <div className="font-medium">{job.title}</div>
            <div className="text-sm text-muted-foreground">
              {job.organizationName || 'Unknown Organization'}
            </div>
            {isExpired && (
              <Badge variant="destructive" className="text-xs">
                Expired
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'location',
      header: 'Location',
      cell: ({ row }: { row: { original: Job } }) => {
        const job = row.original;
        return (
          <div className="flex items-center space-x-1">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{job.location}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'skills',
      header: 'Skills',
      cell: ({ row }: { row: { original: Job } }) => {
        const job = row.original;
        const displaySkills = job.skills.slice(0, 2);
        const remainingCount = job.skills.length - 2;
        return (
          <div className="flex flex-wrap gap-1">
            {displaySkills.map((skill) => (
              <Badge key={skill} variant="secondary" className="text-xs">
                {getSkillLabel(skill)}
              </Badge>
            ))}
            {remainingCount > 0 && (
              <Badge variant="outline" className="text-xs">
                +{remainingCount} more
              </Badge>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'difficultyLevel',
      header: 'Difficulty',
      cell: ({ row }: { row: { original: Job } }) => {
        const job = row.original;
        return (
          <DifficultyLevelBadge
            level={job.interviewConfig?.difficultyLevel || 'normal'}
            size="sm"
          />
        );
      },
    },
    {
      accessorKey: 'salary',
      header: 'Salary',
      cell: ({ row }: { row: { original: Job } }) => {
        const job = row.original;
        return <span className="text-sm">{job.salary || 'Not specified'}</span>;
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }: { row: { original: Job } }) => {
        const job = row.original;
        const isExpired = new Date(job.expiryDate) < new Date();

        // Determine status display based on expiry and current status
        let statusText: string;
        let statusVariant: 'default' | 'secondary' | 'destructive' | 'outline' = 'default';

        if (isExpired) {
          statusText = 'expired';
          statusVariant = 'destructive';
        } else {
          switch (job.status) {
            case 'published':
              statusText = 'published';
              statusVariant = 'default';
              break;
            case 'draft':
              statusText = 'draft';
              statusVariant = 'secondary';
              break;
            case 'expired':
              statusText = 'expired';
              statusVariant = 'destructive';
              break;
            case 'deleted':
              statusText = 'deleted';
              statusVariant = 'outline';
              break;
            default:
              statusText = job.status;
              statusVariant = 'secondary';
          }
        }

        return (
          <Badge variant={statusVariant} className="capitalize">
            {statusText}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'expiryDate',
      header: 'Expires',
      cell: ({ row }: { row: { original: Job } }) => {
        const job = row.original;
        return (
          <div className="flex items-center space-x-1">
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">{format(new Date(job.expiryDate), 'MMM dd, yyyy')}</span>
          </div>
        );
      },
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }: { row: { original: Job } }) => {
        const job = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleViewJob(job)}>
                <Eye className="mr-2 h-4 w-4" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleManageJob(job)}>
                <Settings className="mr-2 h-4 w-4" />
                Manage Job
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleViewApplications(job)}>
                <Users className="mr-2 h-4 w-4" />
                View Applications
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            Error loading jobs: {error.message || 'Unknown error'}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-4', className)}>
      <Card>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search jobs..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="pl-9"
              />
            </div>
            <Select
              value={statusFilter || 'all'}
              onValueChange={(value) => setStatusFilter(value === 'all' ? undefined : value)}
            >
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Jobs</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Table */}
          <TableWrapper
            columns={columns}
            data={jobs}
            pagination={jobsData?.pagination}
            isLoading={false}
            error={error}
            searchPlaceholder="Search jobs..."
            enableSearch={false} // Disable built-in search since we have custom filters
          />
        </CardContent>
      </Card>

      {/* Job Form Modal */}

      {/* Job Details Sheet */}
      <JobDetailsSheet
        job={selectedJob}
        open={detailsSheetOpen}
        onOpenChange={setDetailsSheetOpen}
      />
    </div>
  );
}
