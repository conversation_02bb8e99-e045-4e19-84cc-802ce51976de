'use client';

import { Trash2 } from 'lucide-react';
import { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { toast } from 'sonner';

import { FormValues } from '@/@types/job';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/hooks';
import { useUpdateJob } from '@/hooks/use-jobs';

import { jobStatuses } from './data/job-form-constants';

interface Job {
  id: string;
  title: string;
  organizationId: string;
  organizationName?: string;
  industry: string;
  location: string;
  salary?: string;
  currency?: string;
  skills: string[];
  description: string;
  requirements?: string;
  benefits?: string;
  status: 'draft' | 'published' | 'expired' | 'deleted';
  interviewInstructions?: string;
  questionsConfig: {
    mode: 'manual' | 'ai-mode';
    totalQuestions: number;
    categoryConfigs: Array<{
      type: string;
      numberOfQuestions: number;
    }>;
    questionTypes: string[];
    questions?: Array<{
      id: string;
      type: string;
      question: string;
      isAIGenerated?: boolean;
    }>;
  };
  expiryDate: Date;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date;
  deletedBy?: string;
}

interface JobSettingsTabProps {
  job: Job;
  form: UseFormReturn<FormValues>;
}

export function JobSettingsTab({ job, form }: JobSettingsTabProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const updateJobMutation = useUpdateJob();
  const { user } = useAuth();

  const handleSoftDeleteJob = async () => {
    try {
      await updateJobMutation.mutateAsync({
        id: job.id,
        status: 'deleted' as const, // Force type to allow deleted status
        deletedAt: new Date(),
        deletedBy: user?.id,
      });
      toast.success('Job marked as deleted successfully');
      setShowDeleteDialog(false);
      // Navigate back to jobs list
      window.location.href = '/console/jobs';
    } catch (error) {
      console.error('Error deleting job:', error);
      toast.error('Failed to delete job');
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Job Settings</h3>
        <p className="text-sm text-muted-foreground">
          Manage job status, visibility, and other settings
        </p>
      </div>

      {/* Job Status */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Publication Status</CardTitle>
          <CardDescription>Control when and how your job is visible to candidates</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Job Status</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select job status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {jobStatuses.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        <div className="flex items-center gap-2">
                          <status.icon className={`h-4 w-4 ${status.color}`} />
                          <span>{status.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Draft jobs are not visible to candidates. Published jobs are live and accepting
                  applications.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Expiry Date */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Job Expiry</CardTitle>
          <CardDescription>Set when this job posting should expire</CardDescription>
        </CardHeader>
        <CardContent>
          <FormField
            control={form.control}
            name="expiryDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Expiry Date</FormLabel>
                <FormControl>
                  <Input
                    type="date"
                    value={field.value ? field.value.toISOString().split('T')[0] : ''}
                    onChange={(e) => field.onChange(new Date(e.target.value))}
                  />
                </FormControl>
                <FormDescription>
                  Jobs will automatically become inactive after this date
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </CardContent>
      </Card>

      {/* Job Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Job Statistics</CardTitle>
          <CardDescription>Overview of job performance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">12</div>
              <div className="text-sm text-muted-foreground">Total Views</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">8</div>
              <div className="text-sm text-muted-foreground">Applications</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">3</div>
              <div className="text-sm text-muted-foreground">Shortlisted</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">1</div>
              <div className="text-sm text-muted-foreground">Interviewed</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Danger Zone */}
      <Card className="border-destructive">
        <CardHeader>
          <CardTitle className="text-base text-destructive">Danger Zone</CardTitle>
          <CardDescription>
            Irreversible actions that will permanently affect this job
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button
            variant="destructive"
            onClick={() => setShowDeleteDialog(true)}
            disabled={updateJobMutation.isPending}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Job
          </Button>
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will mark the job as deleted. The job will no longer be visible to
              candidates and will be moved to deleted status. This action can be reversed by
              changing the status back.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleSoftDeleteJob}
              disabled={updateJobMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {updateJobMutation.isPending ? 'Deleting...' : 'Delete Job'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
