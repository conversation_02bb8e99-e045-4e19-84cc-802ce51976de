import { toast } from 'sonner';

import type { Question } from '@/@types/job';

import { questionTypesByIndustry } from '../data/job-form-constants';

// Sample question generator (simulating AI)
export const generateSampleQuestions = (
  type: string,
  jobTitle: string,
  industry: string,
  skills: string[],
  _description: string
): string[] => {
  const skillsText = skills.slice(0, 3).join(', ');

  const questionTemplates: Record<string, string[]> = {
    'technical-coding': [
      `Write a function to solve a common ${skillsText} problem you might encounter in a ${jobTitle} role.`,
      `How would you optimize the performance of a ${skillsText} application?`,
      `Explain the difference between various data structures you'd use in ${skillsText} development.`,
      `Debug this code snippet related to ${skillsText} and explain the issue.`,
    ],
    'technical-system': [
      `Design a scalable system for a ${industry} application using ${skillsText}.`,
      `How would you handle high traffic loads in a ${jobTitle} system?`,
      `Explain how you would architect a microservices solution for ${industry}.`,
      `What database design would you choose for a ${industry} platform and why?`,
    ],
    behavioral: [
      `Tell me about a challenging project you worked on in ${industry}. How did you overcome obstacles?`,
      `Describe a time when you had to work with a difficult team member. How did you handle it?`,
      `How do you prioritize tasks when working on multiple ${industry} projects?`,
      `Give an example of how you've mentored or helped a colleague learn ${skillsText}.`,
    ],
    clinical: [
      `Describe your approach to patient assessment in a ${industry} setting.`,
      `How do you handle emergency situations in clinical practice?`,
      `Explain your experience with medical protocols and procedures.`,
      `How do you ensure patient safety and quality care?`,
    ],
    regulatory: [
      `How do you stay updated with ${industry} regulations and compliance requirements?`,
      `Describe a situation where you had to ensure regulatory compliance in your work.`,
      `What steps do you take to maintain accurate documentation?`,
      `How do you handle regulatory audits or inspections?`,
    ],
    analytical: [
      `Walk me through your process for analyzing ${industry} market trends.`,
      `How do you approach financial modeling for ${industry} investments?`,
      `Describe your experience with risk assessment in ${industry}.`,
      `What tools and methods do you use for data analysis in ${industry}?`,
    ],
    pedagogical: [
      `How do you adapt your teaching methods for different learning styles?`,
      `Describe your approach to lesson planning and curriculum development.`,
      `How do you assess student progress and provide meaningful feedback?`,
      `What strategies do you use to engage students in ${industry} subjects?`,
    ],
    strategic: [
      `How would you develop a marketing strategy for a ${industry} company?`,
      `Describe your approach to market research and competitive analysis.`,
      `What metrics would you use to measure the success of a ${industry} campaign?`,
      `How do you identify and target the right audience for ${industry} products?`,
    ],
    digital: [
      `What digital marketing channels would you recommend for a ${industry} business?`,
      `How do you optimize content for search engines in the ${industry} space?`,
      `Describe your experience with social media marketing in ${industry}.`,
      `How do you measure and improve digital campaign performance?`,
    ],
    experience: [
      `Tell me about your most significant achievement in ${industry}.`,
      `Describe a project where you used ${skillsText} to solve a business problem.`,
      `How has your experience in ${industry} prepared you for this ${jobTitle} role?`,
      `What lessons have you learned from failures or setbacks in your ${industry} career?`,
    ],
    situational: [
      `How would you handle a situation where a project deadline is at risk in ${industry}?`,
      `What would you do if you discovered an error in your work that affects ${industry} operations?`,
      `How would you approach learning a new technology required for this ${jobTitle} position?`,
      `Describe how you would handle conflicting priorities from different stakeholders.`,
    ],
    'culture-fit': [
      `What aspects of our company culture in ${industry} appeal to you most?`,
      `How do you collaborate with cross-functional teams in ${industry} environments?`,
      `What motivates you to work in the ${industry} field?`,
      `How do you handle feedback and continuous learning in your professional development?`,
    ],
  };

  return questionTemplates[type] || questionTemplates.behavioral || [];
};

// AI Question Generation function
export const generateAIQuestions = async (
  numberOfQuestions: number,
  selectedTypes: string[],
  jobTitle: string,
  industry: string,
  skills: string[]
): Promise<Question[]> => {
  try {
    // Get the selected industry's question types
    const industryQuestionTypes =
      questionTypesByIndustry[industry as keyof typeof questionTypesByIndustry] ||
      questionTypesByIndustry.other;

    const generatedQuestions: Question[] = [];

    // Calculate questions per type
    const questionsPerType = Math.floor(numberOfQuestions / selectedTypes.length);
    const remainingQuestions = numberOfQuestions % selectedTypes.length;

    for (let i = 0; i < selectedTypes.length; i++) {
      const questionType = selectedTypes[i];
      const typeInfo = industryQuestionTypes.find((t) => t.value === questionType);
      let questionsForThisType = questionsPerType;

      // Distribute remaining questions to first few types
      if (i < remainingQuestions) {
        questionsForThisType += 1;
      }

      // Generate sample questions based on type and industry
      const sampleQuestions = generateSampleQuestions(
        questionType,
        jobTitle,
        industry,
        skills,
        typeInfo?.description || ''
      );

      for (let j = 0; j < Math.min(questionsForThisType, sampleQuestions.length); j++) {
        generatedQuestions.push({
          id: `ai-${Date.now()}-${questionType}-${j}`,
          type: questionType,
          question: sampleQuestions[j],
          isAIGenerated: true,
        });
      }
    }

    toast.success(`Generated ${generatedQuestions.length} questions successfully!`);
    return generatedQuestions;
  } catch (error) {
    console.error('Error generating questions:', error);
    toast.error('Failed to generate questions. Please try again.');
    return [];
  }
};

// Generate job description using AI
export const generateJobDescription = async (
  title: string,
  organizationName: string,
  skills: string[]
): Promise<string> => {
  try {
    // You can implement AI generation here
    const sampleDescription = `We are seeking a talented ${title} to join our team at ${organizationName}. 

Key Responsibilities:
- Work with cutting-edge technologies and frameworks
- Collaborate with cross-functional teams to deliver high-quality solutions
- Contribute to the design and implementation of scalable systems
- Mentor junior team members and share knowledge

What We're Looking For:
- Strong expertise in ${skills
      .slice(0, 3)
      .map((skill) => skill)
      .join(', ')}
- Proven track record of delivering successful projects
- Excellent problem-solving and communication skills
- Passion for learning and staying updated with industry trends

Join us in building innovative solutions that make a real impact!`;

    return sampleDescription;
  } catch {
    throw new Error('Failed to generate job description');
  }
};
