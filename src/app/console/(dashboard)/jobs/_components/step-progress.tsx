import type { Step } from '@/@types/job';
import { cn } from '@/lib/utils';

interface StepProgressProps {
  steps: Step[];
  currentStep: number;
  onStepClick?: (stepId: number) => void;
}

export function StepProgress({ steps, currentStep, onStepClick }: StepProgressProps) {
  const handleStepClick = (stepId: number) => {
    // Only allow clicking on completed steps (not the current step)
    if (stepId < currentStep && onStepClick) {
      onStepClick(stepId);
    }
  };
  return (
    <div className="px-2 py-3">
      <div className="flex items-center justify-center w-full">
        <div className="flex items-center justify-between max-w-2xl w-full">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={cn('flex items-center', index < steps.length - 1 ? 'flex-1' : '')}
            >
              <div className="flex flex-col items-center">
                <button
                  onClick={() => handleStepClick(step.id)}
                  disabled={step.id >= currentStep || !onStepClick}
                  className={cn(
                    'flex items-center justify-center w-8 h-8 rounded-full border-2 text-xs font-medium transition-all relative z-10 bg-background',
                    currentStep >= step.id
                      ? 'bg-primary text-primary-foreground border-primary'
                      : 'bg-background text-muted-foreground border-muted-foreground',
                    // Add hover effects for clickable completed steps
                    step.id < currentStep && onStepClick
                      ? 'cursor-pointer hover:scale-110 hover:shadow-md hover:bg-primary/90'
                      : step.id >= currentStep
                        ? 'cursor-default'
                        : 'cursor-not-allowed'
                  )}
                  type="button"
                >
                  {step.id}
                </button>
                <div className="mt-2 text-center">
                  <p className="text-xs font-medium text-foreground truncate max-w-[80px]">
                    {step.title}
                  </p>
                </div>
              </div>
              {index < steps.length - 1 && (
                <div className="flex-1 relative">
                  <div
                    className={cn(
                      'absolute top-[-16px] left-4 right-4 h-[2px] transition-colors',
                      currentStep > step.id ? 'bg-primary' : 'bg-border'
                    )}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
