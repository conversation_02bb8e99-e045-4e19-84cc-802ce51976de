import { UseFormReturn } from 'react-hook-form';

import type { FormValues } from '@/@types/job';
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { getDifficultyLevelIcon, getDifficultyLevelInfo } from '@/lib/interview-utils';

import { difficultyLevels } from './data/job-form-constants';

interface InterviewStepProps {
  form: UseFormReturn<FormValues>;
}

export function InterviewStep({ form }: InterviewStepProps) {
  const watchScreenMonitoring = form.watch('interviewConfig.screenMonitoring');
  const watchCameraMonitoring = form.watch('interviewConfig.cameraMonitoring');

  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="interviewConfig.duration"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Interview Duration (minutes) *</FormLabel>
            <Select
              onValueChange={(value) => field.onChange(parseInt(value))}
              value={field.value?.toString()}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select duration" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="5">5 minutes</SelectItem>
                <SelectItem value="10">10 minutes</SelectItem>
                <SelectItem value="15">15 minutes</SelectItem>
                <SelectItem value="20">20 minutes</SelectItem>
                <SelectItem value="30">30 minutes</SelectItem>
                <SelectItem value="45">45 minutes</SelectItem>
                <SelectItem value="60">60 minutes</SelectItem>
              </SelectContent>
            </Select>
            <FormDescription>
              Set the maximum duration for the AI interview session.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="interviewConfig.difficultyLevel"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-base font-semibold">Interview Difficulty Level *</FormLabel>
            <div className="space-y-3">
              <Select onValueChange={field.onChange} value={field.value}>
                <FormControl>
                  <SelectTrigger className="h-auto min-h-[3rem] p-3 border-2 hover:border-primary/50 transition-colors">
                    <SelectValue placeholder="Select difficulty level">
                      {field.value && (
                        <div className="flex items-center gap-3">
                          <span className="text-xl">{getDifficultyLevelIcon(field.value)}</span>
                          <div className="flex flex-col items-start">
                            <span className="font-semibold text-base">
                              {getDifficultyLevelInfo(field.value).label}
                            </span>
                            <span className="text-sm text-muted-foreground">
                              {getDifficultyLevelInfo(field.value).description}
                            </span>
                          </div>
                        </div>
                      )}
                    </SelectValue>
                  </SelectTrigger>
                </FormControl>
                <SelectContent className="max-w-md">
                  {difficultyLevels.map((level) => {
                    const { colorClass } = getDifficultyLevelInfo(level.value);
                    const icon = getDifficultyLevelIcon(level.value);
                    return (
                      <SelectItem
                        key={level.value}
                        value={level.value}
                        className="cursor-pointer hover:bg-muted/50 p-3"
                      >
                        <div className="flex items-center gap-3 w-full">
                          <span className="text-xl">{icon}</span>
                          <div className="flex flex-col items-start flex-1">
                            <div className="flex items-center gap-3 w-full">
                              <span className="font-semibold text-base">{level.label}</span>
                              <div
                                className={`px-3 py-1 rounded-full text-xs font-bold ${colorClass}`}
                              >
                                {level.label.toUpperCase()}
                              </div>
                            </div>
                            <span className="text-sm text-muted-foreground mt-1">
                              {level.description}
                            </span>
                          </div>
                        </div>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>

              {/* Preview of selected difficulty */}
              {field.value && (
                <div className="mt-3 p-4 rounded-lg border-2 border-dashed border-muted-foreground/30 bg-muted/20">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{getDifficultyLevelIcon(field.value)}</span>
                    <div className="flex flex-col">
                      <div className="flex items-center gap-2">
                        <span className="font-bold text-lg">
                          {getDifficultyLevelInfo(field.value).label} Level Selected
                        </span>
                        <div
                          className={`px-3 py-1 rounded-full text-xs font-bold ${getDifficultyLevelInfo(field.value).colorClass}`}
                        >
                          {getDifficultyLevelInfo(field.value).label.toUpperCase()}
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {getDifficultyLevelInfo(field.value).description}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
            <FormDescription className="text-sm">
              💡 Choose the appropriate difficulty level to match your position requirements and
              candidate experience level.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="interviewConfig.instructions"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Interview Instructions (Optional)</FormLabel>
            <FormControl>
              <Textarea
                placeholder="Provide specific instructions for the interview process"
                className="min-h-[120px]"
                {...field}
              />
            </FormControl>
            <FormDescription>
              Instructions that will be shown to candidates before the interview.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="space-y-6 rounded-lg border p-4">
        <h3 className="text-lg font-medium">Monitoring Settings</h3>

        <FormField
          control={form.control}
          name="interviewConfig.screenMonitoring"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Screen Monitoring</FormLabel>
                <FormDescription>Enable screen monitoring during the interview</FormDescription>
              </div>
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </FormItem>
          )}
        />

        {watchScreenMonitoring && (
          <div className="ml-4 space-y-4">
            <FormField
              control={form.control}
              name="interviewConfig.screenMonitoringMode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Screen Monitoring Mode</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select mode" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="photo">Photo Capture</SelectItem>
                      <SelectItem value="video">Video Recording</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose how to monitor the candidate&apos;s screen
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="interviewConfig.screenMonitoringInterval"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Screen Monitoring Interval</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    value={field.value?.toString()}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select interval" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="30">Every 30 seconds</SelectItem>
                      <SelectItem value="60">Every 60 seconds</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>How often to capture screen activity</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        <FormField
          control={form.control}
          name="interviewConfig.cameraMonitoring"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Camera Monitoring</FormLabel>
                <FormDescription>Enable camera monitoring during the interview</FormDescription>
              </div>
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </FormItem>
          )}
        />

        {watchCameraMonitoring && (
          <div className="ml-4 space-y-4">
            <FormField
              control={form.control}
              name="interviewConfig.cameraMonitoringMode"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Camera Monitoring Mode</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select mode" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="photo">Photo Capture</SelectItem>
                      <SelectItem value="video">Video Recording</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose how to monitor the candidate&apos;s camera
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="interviewConfig.cameraMonitoringInterval"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Camera Monitoring Interval</FormLabel>
                  <Select
                    onValueChange={(value) => field.onChange(parseInt(value))}
                    value={field.value?.toString()}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select interval" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="30">Every 30 seconds</SelectItem>
                      <SelectItem value="60">Every 60 seconds</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>How often to capture camera activity</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}
      </div>
    </div>
  );
}
