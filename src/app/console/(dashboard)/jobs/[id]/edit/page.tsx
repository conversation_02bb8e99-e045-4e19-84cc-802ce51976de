'use client';

import { <PERSON>Left } from 'lucide-react';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { Suspense } from 'react';

import type { JobForComponents } from '@/@types/job';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useJob } from '@/hooks/use-jobs';

import { JobEditForm } from '../../_components';

// Helper function to transform job data for components
const transformJobForComponent = (job: Record<string, unknown>): JobForComponents => {
  return {
    ...job,
    organizationId:
      typeof job.organizationId === 'string'
        ? job.organizationId
        : job.organizationId?.toString() || '',
    recruiter: typeof job.recruiter === 'string' ? job.recruiter : job.recruiter?.toString() || '',
  } as JobForComponents;
};

function JobEditPageContent() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();

  const jobId = params.id as string;
  const activeTab = searchParams.get('tab') || 'basic';

  const { data: job, isLoading, error } = useJob(jobId, true);

  const handleTabChange = (value: string) => {
    const newSearchParams = new URLSearchParams(searchParams.toString());
    newSearchParams.set('tab', value);
    router.push(`/console/jobs/${jobId}/edit?${newSearchParams.toString()}`);
  };

  const handleBack = () => {
    router.push('/console/jobs');
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !job) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              {error ? `Error loading job: ${error.message}` : 'Job not found'}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Jobs
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Job</h1>
            <p className="text-muted-foreground">{job.title}</p>
          </div>
        </div>

        {/* Job Edit Tabs */}
        <Card>
          <CardHeader>
            <CardTitle>Job Details</CardTitle>
            <CardDescription>
              Manage all aspects of your job posting using the tabs below
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
              <TabsList className="grid w-full grid-cols-6">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="description">Description</TabsTrigger>
                <TabsTrigger value="interview">Interview</TabsTrigger>
                <TabsTrigger value="questions">Questions</TabsTrigger>
                <TabsTrigger value="applications">Applications</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
              </TabsList>

              <div className="mt-6">
                <JobEditForm
                  job={transformJobForComponent(job as unknown as Record<string, unknown>)}
                  activeTab={activeTab}
                />
              </div>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function JobEditPage() {
  return (
    <Suspense
      fallback={
        <div className="container mx-auto py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
          </div>
        </div>
      }
    >
      <JobEditPageContent />
    </Suspense>
  );
}
