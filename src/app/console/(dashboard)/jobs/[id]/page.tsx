'use client';

import { ArrowLeft, Calendar, MapPin, Users } from 'lucide-react';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { Suspense } from 'react';

import type { JobForComponents } from '@/@types/job';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useJob } from '@/hooks/use-jobs';

import { JobEditForm } from '../_components/job-edit-form';

// Helper function to transform job data for components
const transformJobForComponent = (job: Record<string, unknown>): JobForComponents => {
  return {
    ...job,
    organizationId:
      typeof job.organizationId === 'string'
        ? job.organizationId
        : job.organizationId?.toString() || '',
    recruiter: typeof job.recruiter === 'string' ? job.recruiter : job.recruiter?.toString() || '',
  } as JobForComponents;
};

function JobDetailPageContent() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();

  const jobId = params.id as string;
  const activeTab = searchParams.get('tab') || 'basic';

  const { data: job, isLoading, error } = useJob(jobId, true);

  const handleTabChange = (value: string) => {
    if (value === 'applications') {
      router.push(`/jobs/${jobId}/applications`);
      return;
    }

    const newSearchParams = new URLSearchParams(searchParams.toString());
    newSearchParams.set('tab', value);
    router.push(`/jobs/${jobId}?${newSearchParams.toString()}`);
  };

  const handleBack = () => {
    router.push('/jobs');
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !job) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              {error ? `Error loading job: ${error.message}` : 'Job not found'}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-background to-muted/20 border rounded-lg p-6">
        <div className="flex items-start justify-between">
          <div className="space-y-4 flex-1">
            {/* Title with Back Button and Status */}
            <div className="space-y-3">
              <div className="flex items-center gap-4 flex-wrap">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBack}
                  className="text-muted-foreground hover:text-foreground -ml-2"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Jobs
                </Button>
                <div className="flex items-start gap-3 flex-wrap flex-1">
                  <h1 className="text-3xl font-bold tracking-tight text-foreground leading-tight">
                    {job.title}
                  </h1>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={
                        job.status === 'published'
                          ? 'default'
                          : job.status === 'draft'
                            ? 'secondary'
                            : 'destructive'
                      }
                      className={`text-xs font-medium ${
                        job.status === 'draft'
                          ? 'bg-amber-100 text-amber-800 hover:bg-amber-200 border-amber-200'
                          : job.status === 'expired'
                            ? 'bg-red-100 text-red-800 hover:bg-red-200 border-red-200'
                            : job.status === 'published'
                              ? 'bg-green-100 text-green-800 hover:bg-green-200 border-green-200'
                              : ''
                      }`}
                    >
                      {job.status === 'published' ? 'Active' : job.status}
                    </Badge>
                    {new Date(job.expiryDate) < new Date() && (
                      <Badge variant="destructive" className="text-xs">
                        Expired
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              {/* Job Meta Information */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <MapPin className="h-4 w-4" />
                  <span className="font-medium">{job.location}</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <Calendar className="h-4 w-4" />
                  <span>Posted {new Date(job.createdAt).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-2 text-muted-foreground">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <Users className="h-4 w-4" />
                  <span>8 Applications</span>
                </div>
              </div>

              {/* Additional Job Info */}
              <div className="flex items-center gap-6 text-sm text-muted-foreground pt-2 border-t border-border/40">
                <div className="flex items-center gap-1">
                  <span className="font-medium text-foreground">Industry:</span>
                  <span>{job.industry}</span>
                </div>
                {job.salary && (
                  <div className="flex items-center gap-1">
                    <span className="font-medium text-foreground">Salary:</span>
                    <span>{job.salary}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-3 ml-4">
            <Button
              onClick={() => router.push(`/console/jobs/${jobId}/applications`)}
              variant="outline"
              className="whitespace-nowrap"
            >
              <Users className="h-4 w-4 mr-2" />
              View Applications
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Job Details</CardTitle>
              <CardDescription>Manage all aspects of your job posting</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="description">Description</TabsTrigger>
              <TabsTrigger value="interview">Interview</TabsTrigger>
              <TabsTrigger value="questions">Questions</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <div className="mt-6">
              <JobEditForm
                job={transformJobForComponent(job as unknown as Record<string, unknown>)}
                activeTab={activeTab}
              />
            </div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

export default function JobDetailPage() {
  return (
    <Suspense
      fallback={
        <div className="container mx-auto py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
          </div>
        </div>
      }
    >
      <JobDetailPageContent />
    </Suspense>
  );
}
