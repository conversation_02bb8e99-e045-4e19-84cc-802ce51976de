'use client';

import { ArrowLeft } from 'lucide-react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { Suspense } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useJob } from '@/hooks/use-jobs';

import { JobApplicationsTab } from '../../_components/job-applications-tab';

function JobApplicationsPageContent() {
  const params = useParams();
  const router = useRouter();

  const jobId = params.id as string;
  const { data: job, isLoading, error } = useJob(jobId, true);

  const handleBack = () => {
    router.push(`/console/jobs/${jobId}`);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !job) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              {error ? `Error loading job: ${error.message}` : 'Job not found'}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Enhanced Header */}
      <div className="bg-gradient-to-r from-background to-muted/20 border rounded-lg p-6">
        <div className="flex items-start justify-between">
          <div className="space-y-4 flex-1">
            {/* Title with Back Button */}
            <div className="space-y-3">
              <div className="flex items-center gap-4 flex-wrap">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBack}
                  className="text-muted-foreground hover:text-foreground -ml-2"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Job
                </Button>
                <div className="flex-1">
                  <h1 className="text-3xl font-bold tracking-tight text-foreground leading-tight">
                    Applications
                  </h1>
                </div>
              </div>

              {/* Job Title and Description */}
              <div className="space-y-2">
                <p className="text-lg font-medium text-foreground">{job.title}</p>
                <p className="text-sm text-muted-foreground">
                  Manage and review candidate applications for this position
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Candidate Applications</CardTitle>
              <CardDescription>
                Review, shortlist, and manage applications for this job posting
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <JobApplicationsTab jobId={jobId} />
        </CardContent>
      </Card>
    </div>
  );
}

export default function JobApplicationsPage() {
  return (
    <Suspense
      fallback={
        <div className="container mx-auto py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
          </div>
        </div>
      }
    >
      <JobApplicationsPageContent />
    </Suspense>
  );
}
