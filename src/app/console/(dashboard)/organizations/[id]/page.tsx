import { notFound } from 'next/navigation';

import { OrganizationDetailsClient } from './_components/organization-details-client';

interface OrganizationPageProps {
  params: Promise<{ id: string }>;
}

export default async function OrganizationPage({ params }: OrganizationPageProps) {
  const { id } = await params;

  // Validate if the id is a valid MongoDB ObjectId (24 characters)
  if (!id || id.length !== 24) {
    notFound();
  }

  return <OrganizationDetailsClient organizationId={id} />;
}
