'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Building2, Edit, Loader2 } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useCallback, useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardHeader } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useOrganization } from '@/hooks/use-organizations';

import { AddOrganizationForm } from '../../_components/add-organization-form';
import OrganizationJobsTab from './organization-jobs-tab';
import OrganizationMemberTab from './organization-member-tab';
import OrganizationOverviewTab from './organization-overview-tab';

interface OrganizationDetailsClientProps {
  organizationId: string;
}

export function OrganizationDetailsClient({ organizationId }: OrganizationDetailsClientProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'overview');

  const { data: organization, isLoading, error } = useOrganization(organizationId);

  // Update URL when tab changes
  const handleTabChange = useCallback(
    (tab: string) => {
      setActiveTab(tab);
      const url = new URL(window.location.href);
      url.searchParams.set('tab', tab);
      router.replace(url.pathname + url.search, { scroll: false });
    },
    [router]
  );

  // Update active tab when URL changes
  useEffect(() => {
    const tabFromUrl = searchParams.get('tab');
    if (tabFromUrl && ['overview', 'members', 'jobs'].includes(tabFromUrl)) {
      setActiveTab(tabFromUrl);
    }
  }, [searchParams]);

  if (isLoading) {
    return (
      <div className="flex h-48 items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !organization) {
    return (
      <div className="flex h-48 items-center justify-center">
        <div className="text-center">
          <p className="text-lg font-medium">Organization not found</p>
          <Button
            variant="outline"
            onClick={() => router.push('/console/organizations')}
            className="mt-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Organizations
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Combined Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => router.push('/console/organizations')}
              className="h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-muted/50">
              {organization.logo ? (
                // eslint-disable-next-line @next/next/no-img-element
                <img
                  src={organization.logo}
                  alt={organization.name}
                  className="h-12 w-12 rounded object-cover"
                />
              ) : (
                <Building2 className="h-8 w-8 text-muted-foreground" />
              )}
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <h1 className="text-2xl font-bold tracking-tight">{organization.name}</h1>
                <Badge variant="secondary">{organization.slug}</Badge>
              </div>
              <p className="text-muted-foreground">
                Created {new Date(organization.createdAt).toLocaleDateString()}
                {organization.description && ` • ${organization.description}`}
              </p>
            </div>
            <AddOrganizationForm
              organization={organization}
              trigger={
                <Button variant="outline">
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Organization
                </Button>
              }
            />
          </div>
        </CardHeader>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="jobs">Jobs</TabsTrigger>
        </TabsList>

        <OrganizationOverviewTab organization={organization} />
        <OrganizationMemberTab organization={organization} />
        <OrganizationJobsTab organization={organization} />
      </Tabs>
    </div>
  );
}
