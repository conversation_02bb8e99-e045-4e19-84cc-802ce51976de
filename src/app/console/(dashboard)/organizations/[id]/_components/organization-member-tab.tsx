import { ColumnDef } from '@tanstack/react-table';
import { CheckCircle, User, XCircle } from 'lucide-react';

import { IRecruiter, Organization, OrganizationMemberSortField, Recruiter } from '@/@types';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { SortableHeader, TableWrapper } from '@/components/ui/table-wrapper';
import { TabsContent } from '@/components/ui/tabs';
import { useOrganizationMembers, useTableState } from '@/hooks';

import AddMemberForm from '../../_components/add-member-form';

export const recruitersColumns: ColumnDef<Recruiter>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Recruiter
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const recruiter = row.original;
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={recruiter.image} />
            <AvatarFallback>
              <User className="h-5 w-5" />
            </AvatarFallback>
          </Avatar>
          <div className="min-w-0 flex-1">
            <div className="font-medium truncate">{recruiter.name}</div>
            <div className="text-sm text-muted-foreground truncate">{recruiter.email}</div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Email
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const email = row.getValue('email') as string;
      return <div className="font-mono text-sm">{email}</div>;
    },
  },
  {
    accessorKey: 'emailVerified',
    header: 'Status',
    cell: ({ row }) => {
      const emailVerified = row.getValue('emailVerified') as boolean;
      return (
        <div className="flex items-center space-x-2">
          {emailVerified ? (
            <>
              <CheckCircle className="h-4 w-4 text-green-500" />
              <Badge variant="outline" className="text-green-600 border-green-200">
                Verified
              </Badge>
            </>
          ) : (
            <>
              <XCircle className="h-4 w-4 text-red-500" />
              <Badge variant="outline" className="text-red-600 border-red-200">
                Unverified
              </Badge>
            </>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Created
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const createdAt = row.getValue('createdAt') as Date;
      return (
        <div className="text-sm text-muted-foreground">
          {new Date(createdAt).toLocaleDateString()}
        </div>
      );
    },
  },
];
export default function OrganizationMemberTab({ organization }: { organization: Organization }) {
  const { apiParams } = useTableState<OrganizationMemberSortField>();

  const { data, isLoading, error, refetch } = useOrganizationMembers(apiParams);

  console.log('Organization Members Data:', data);

  const recruiters = (data?.data ?? []).filter(
    (item): item is IRecruiter => typeof item !== 'string'
  );
  const pagination = data?.pagination;
  return (
    <TabsContent value="members" className="space-y-6">
      <div className="container mx-auto py-2">
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Team Members</h1>
            <p className="text-muted-foreground">
              Manage team members and their roles within {organization.name}
            </p>
          </div>
          <AddMemberForm organizationId={organization.id} />
        </div>

        <TableWrapper
          columns={recruitersColumns}
          data={recruiters}
          pagination={pagination}
          isLoading={isLoading}
          error={error}
          searchPlaceholder="Search recruiters..."
          onRetry={() => refetch()}
        />
      </div>
    </TabsContent>
  );
}
