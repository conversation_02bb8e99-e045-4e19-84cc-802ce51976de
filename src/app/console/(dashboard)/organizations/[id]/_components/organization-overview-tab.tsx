import { Building2, Calendar, Globe } from 'lucide-react';

import { Organization } from '@/@types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { TabsContent } from '@/components/ui/tabs';
export default function OrganizationOverviewTab({ organization }: { organization: Organization }) {
  return (
    <TabsContent value="overview" className="space-y-6">
      {/* Organization Information */}
      <Card>
        <CardHeader>
          <CardTitle>Organization Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-3">
            <div className="flex items-center gap-2 text-sm">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Organization ID:</span>
              <span className="text-muted-foreground font-mono">{organization.id}</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Globe className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Slug:</span>
              <span className="text-muted-foreground font-mono">{organization.slug}</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Created:</span>
              <span className="text-muted-foreground">
                {new Date(organization.createdAt).toLocaleString()}
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Last Updated:</span>
              <span className="text-muted-foreground">
                {new Date(organization.updatedAt).toLocaleString()}
              </span>
            </div>
            {organization.metadata && (
              <div className="flex items-start gap-2 text-sm">
                <Globe className="h-4 w-4 text-muted-foreground mt-0.5" />
                <span className="font-medium">Metadata:</span>
                <span className="text-muted-foreground">{organization.metadata}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </TabsContent>
  );
}
