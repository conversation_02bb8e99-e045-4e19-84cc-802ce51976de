'use client';

import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Loader2, Plus } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import {
  type AddOrganizationFormData,
  addOrganizationSchema,
  type Organization,
} from '@/@types/organization';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useCreateOrganization, useUpdateOrganization } from '@/hooks/use-organizations';

type AddOrganizationForm = AddOrganizationFormData;

interface AddOrganizationFormProps {
  organization?: Organization; // For editing mode
  onSuccess?: () => void;
  trigger?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function AddOrganizationForm({
  organization,
  onSuccess,
  trigger,
  open: controlledOpen,
  onOpenChange,
}: AddOrganizationFormProps) {
  const [internalOpen, setInternalOpen] = useState(false);
  const open = controlledOpen !== undefined ? controlledOpen : internalOpen;
  const setOpen = onOpenChange || setInternalOpen;

  const isEditing = !!organization;

  const form = useForm<AddOrganizationForm>({
    resolver: zodResolver(addOrganizationSchema),
    defaultValues: {
      name: organization?.name || '',
      slug: organization?.slug || '',
      description: organization?.description || '',
      logo: organization?.logo || '',
      metadata: organization?.metadata || '',
    },
  });

  // Reset form when organization changes (for editing)
  useEffect(() => {
    if (organization) {
      form.reset({
        name: organization.name,
        slug: organization.slug,
        description: organization.description || '',
        logo: organization.logo || '',
        metadata: organization.metadata || '',
      });
    }
  }, [organization, form]);

  const createOrganization = useCreateOrganization();
  const updateOrganization = useUpdateOrganization();

  const onSubmit = (data: AddOrganizationForm) => {
    if (isEditing && organization) {
      // Update existing organization
      updateOrganization.mutate(
        { id: organization.id, ...data },
        {
          onSuccess: (result) => {
            toast.success('Organization updated successfully!', {
              description: `${result.name} has been updated.`,
            });

            // Reset form and close dialog
            form.reset();
            setOpen(false);

            // Call success callback if provided
            onSuccess?.();
          },
          onError: (error) => {
            toast.error('Failed to update organization', {
              description: error.message,
            });
          },
        }
      );
    } else {
      // Create new organization
      createOrganization.mutate(data, {
        onSuccess: (result) => {
          toast.success('Organization created successfully!', {
            description: `${result.name} has been added to your organizations.`,
          });

          // Reset form and close dialog
          form.reset();
          setOpen(false);

          // Call success callback if provided
          onSuccess?.();
        },
        onError: (error) => {
          toast.error('Failed to create organization', {
            description: error.message,
          });
        },
      });
    }
  };

  // Auto-generate slug from name
  const handleNameChange = (name: string) => {
    const slug = name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim();

    form.setValue('slug', slug);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {!isEditing && (
        <DialogTrigger asChild>
          {trigger || (
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Organization
            </Button>
          )}
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Edit Organization' : 'Add New Organization'}</DialogTitle>
          <DialogDescription>
            {isEditing
              ? 'Update the organization details below.'
              : 'Create a new organization. The slug will be auto-generated from the name, but you can customize it.'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Organization Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Acme Corporation"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        handleNameChange(e.target.value);
                      }}
                    />
                  </FormControl>
                  <FormDescription>The display name for your organization.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Slug</FormLabel>
                  <FormControl>
                    <Input placeholder="acme-corporation" {...field} />
                  </FormControl>
                  <FormDescription>
                    URL-friendly identifier. Must be lowercase, numbers, and hyphens only.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="A brief description of the organization..."
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>Optional description of the organization.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="logo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Logo URL</FormLabel>
                  <FormControl>
                    <Input placeholder="https://example.com/logo.png" type="url" {...field} />
                  </FormControl>
                  <FormDescription>
                    Optional URL to the organization&apos;s logo image.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="metadata"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Metadata</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional metadata (JSON, notes, etc.)"
                      className="resize-none"
                      rows={2}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>Optional metadata or additional information.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={createOrganization.isPending || updateOrganization.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createOrganization.isPending || updateOrganization.isPending}
              >
                {(createOrganization.isPending || updateOrganization.isPending) && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {isEditing ? 'Update Organization' : 'Create Organization'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
