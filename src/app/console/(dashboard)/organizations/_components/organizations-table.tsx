'use client';

import { type ColumnDef } from '@tanstack/react-table';
import { Edit, Eye, MoreHorizontal, Settings } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import type { Organization, OrganizationSortField } from '@/@types/organization';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SortableHeader, TableWrapper } from '@/components/ui/table-wrapper';
import { useOrganizations } from '@/hooks/use-organizations';
import { useTableState } from '@/hooks/use-table-state';

import { AddOrganizationForm } from './add-organization-form';
import { OrganizationDetailsSheet } from './organization-details-sheet';

export const organizationsColumns: ColumnDef<Organization>[] = [
  {
    accessorKey: 'name',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Organization
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const org = row.original;
      return (
        <div className="flex items-center space-x-3">
          <Avatar className="h-10 w-10">
            <AvatarImage src={org.logo} />
            <AvatarFallback>
              {org.name
                ?.split(' ')
                .map((n: string) => n[0])
                .join('')
                .slice(0, 2)}
            </AvatarFallback>
          </Avatar>
          <div className="min-w-0 flex-1">
            <div className="font-medium truncate">{org.name}</div>
            <div className="text-sm text-muted-foreground truncate">{org.slug}</div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'slug',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Slug
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const slug = row.getValue('slug') as string;
      return <div className="font-mono text-sm">{slug}</div>;
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <SortableHeader column={column} className="min-w-0">
        Created
      </SortableHeader>
    ),
    cell: ({ row }) => {
      const createdAt = row.getValue('createdAt') as Date;
      return (
        <div className="text-sm text-muted-foreground">
          {new Date(createdAt).toLocaleDateString()}
        </div>
      );
    },
  },
  {
    id: 'actions',
    enableHiding: false,
    cell: ({ row }) => {
      const organization = row.original;
      return <OrganizationActions organization={organization} />;
    },
  },
];

export function OrganizationsTable() {
  const { apiParams } = useTableState<OrganizationSortField>();

  const { data, isLoading, error, refetch } = useOrganizations(apiParams);

  const organizations = data?.data ?? [];
  const pagination = data?.pagination;

  return (
    <TableWrapper
      columns={organizationsColumns}
      data={organizations}
      pagination={pagination}
      isLoading={isLoading}
      error={error}
      searchPlaceholder="Search organizations..."
      onRetry={() => refetch()}
    />
  );
}

interface OrganizationActionsProps {
  organization: Organization;
}

function OrganizationActions({ organization }: OrganizationActionsProps) {
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);
  const router = useRouter();
  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setDetailsOpen(true)}>
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              router.push(`organizations/${organization.id}`);
            }}
          >
            <Settings className="mr-2 h-4 w-4" />
            Manage
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setEditOpen(true)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <OrganizationDetailsSheet
        organizationId={organization.id}
        open={detailsOpen}
        onOpenChange={setDetailsOpen}
        onEdit={() => {
          setDetailsOpen(false);
          setEditOpen(true);
        }}
      />

      <AddOrganizationForm organization={organization} open={editOpen} onOpenChange={setEditOpen} />
    </>
  );
}
