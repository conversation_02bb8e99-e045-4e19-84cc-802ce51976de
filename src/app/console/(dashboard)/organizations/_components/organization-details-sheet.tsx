'use client';

import { format, isValid } from 'date-fns';
import { Calendar, Edit } from 'lucide-react';

import type { Organization } from '@/@types/organization';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import { VisuallyHidden } from '@/components/ui/visually-hidden';
import { useOrganization } from '@/hooks/use-organizations';

// Helper function to safely format dates
function formatDate(dateValue: string | Date | undefined | null): string {
  if (!dateValue) return 'Invalid date';

  const date = new Date(dateValue);
  if (!isValid(date)) return 'Invalid date';

  return format(date, 'PPP p');
}

// Helper function to safely parse and format JSON
function formatMetadata(metadata: string | undefined | null): { content: string; isJson: boolean } {
  if (!metadata) return { content: 'No metadata available', isJson: false };

  try {
    const parsed = JSON.parse(metadata);
    return { content: JSON.stringify(parsed, null, 2), isJson: true };
  } catch {
    return { content: metadata, isJson: false }; // Return as plain text if not valid JSON
  }
}

interface OrganizationDetailsSheetProps {
  organizationId: string | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit?: (organization: Organization) => void;
}

export function OrganizationDetailsSheet({
  organizationId,
  open,
  onOpenChange,
  onEdit,
}: OrganizationDetailsSheetProps) {
  const {
    data: organization,
    isLoading,
    error,
  } = useOrganization(organizationId || '', open && !!organizationId);

  if (isLoading) {
    return (
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent className="min-w-[400px] sm:min-w-[540px]">
          <SheetHeader>
            <VisuallyHidden>
              <SheetTitle>Loading Organization Details</SheetTitle>
            </VisuallyHidden>
          </SheetHeader>
          <div className="flex items-center justify-center h-full">
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-sm text-muted-foreground">Loading organization details...</p>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  if (error || !organization) {
    return (
      <Sheet open={open} onOpenChange={onOpenChange}>
        <SheetContent className="min-w-[400px] sm:min-w-[540px]">
          <SheetHeader>
            <VisuallyHidden>
              <SheetTitle>Organization Details Error</SheetTitle>
            </VisuallyHidden>
          </SheetHeader>
          <div className="flex items-center justify-center h-full">
            <div className="text-center space-y-4">
              <div className="h-12 w-12 rounded-full bg-red-100 dark:bg-red-950 mx-auto flex items-center justify-center">
                <svg
                  className="h-6 w-6 text-red-600 dark:text-red-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.502 0L4.312 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <div>
                <p className="font-medium text-foreground">Failed to load organization</p>
                <p className="text-sm text-muted-foreground">
                  The organization details could not be retrieved
                </p>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="min-w-[400px] sm:min-w-[540px] overflow-y-auto">
        <SheetHeader className="pb-6 border-b">
          <SheetTitle className="text-xl font-semibold text-left">Organization Details</SheetTitle>
          <SheetDescription className="text-left">
            View detailed information about this organization
          </SheetDescription>
        </SheetHeader>

        <div className="px-1 py-6 space-y-8 relative">
          {/* Edit Button - Top Right of Content */}
          {onEdit && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onEdit(organization)}
              className="absolute top-2 right-2 z-10"
            >
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          )}
          {/* Organization Header */}
          <div className="bg-gradient-to-r from-slate-50 to-gray-50 dark:from-slate-900 dark:to-gray-900 rounded-lg p-6 border">
            <div className="flex items-center space-x-4">
              <Avatar className="h-20 w-20 border-2 border-background shadow-lg">
                <AvatarImage src={organization.logo} />
                <AvatarFallback className="text-xl font-semibold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                  {organization.name
                    ?.split(' ')
                    .map((n: string) => n[0])
                    .join('')
                    .slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <h3 className="text-2xl font-bold text-foreground">{organization.name}</h3>
                <div className="flex items-center mt-2">
                  <div className="bg-slate-100 dark:bg-slate-800 px-3 py-1 rounded-full">
                    <p className="text-sm font-mono text-muted-foreground">{organization.slug}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Basic Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold flex items-center">
              <div className="h-1 w-1 rounded-full bg-blue-500 mr-3"></div>
              Basic Information
            </h4>
            <div className="bg-card border rounded-lg p-4">
              <div className="grid grid-cols-1 gap-4 text-sm">
                <div className="flex justify-between items-center py-2 border-b border-border/50 last:border-b-0">
                  <label className="font-medium text-muted-foreground">Organization ID</label>
                  <p className="font-mono text-xs bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded">
                    {organization.id}
                  </p>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-border/50 last:border-b-0">
                  <label className="font-medium text-muted-foreground">Slug</label>
                  <p className="font-mono text-sm">{organization.slug}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Description */}
          {organization.description && (
            <div className="space-y-4">
              <h4 className="text-lg font-semibold flex items-center">
                <div className="h-1 w-1 rounded-full bg-green-500 mr-3"></div>
                Description
              </h4>
              <div className="bg-card border rounded-lg p-4">
                <p className="text-sm text-foreground leading-relaxed">
                  {organization.description}
                </p>
              </div>
            </div>
          )}

          {/* Metadata */}
          {organization.metadata && (
            <div className="space-y-4">
              <h4 className="text-lg font-semibold flex items-center">
                <div className="h-1 w-1 rounded-full bg-purple-500 mr-3"></div>
                Metadata
              </h4>
              <div className="bg-card border rounded-lg p-4">
                {(() => {
                  const metadata = formatMetadata(organization.metadata);
                  return (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground font-medium">
                          {metadata.isJson ? 'JSON Data' : 'Plain Text'}
                        </span>
                        {metadata.isJson && (
                          <span className="text-xs text-green-600 dark:text-green-400">
                            ✓ Valid JSON
                          </span>
                        )}
                      </div>
                      <div className="bg-slate-50 dark:bg-slate-900 rounded-md p-3 border">
                        <pre className="text-xs overflow-auto text-foreground font-mono whitespace-pre-wrap">
                          {metadata.content}
                        </pre>
                      </div>
                    </div>
                  );
                })()}
              </div>
            </div>
          )}

          {/* Timestamps */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold flex items-center">
              <div className="h-1 w-1 rounded-full bg-orange-500 mr-3"></div>
              Timeline
            </h4>
            <div className="bg-card border rounded-lg p-4">
              <div className="space-y-4">
                <div className="flex items-start space-x-3 p-3 rounded-md bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800">
                  <div className="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                    <Calendar className="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-green-900 dark:text-green-100">Created</p>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      {formatDate(organization.createdAt)}
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3 p-3 rounded-md bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800">
                  <div className="h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                    <Calendar className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-blue-900 dark:text-blue-100">Last Updated</p>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      {formatDate(organization.updatedAt)}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
