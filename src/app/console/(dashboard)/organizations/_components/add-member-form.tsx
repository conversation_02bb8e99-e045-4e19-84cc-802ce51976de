'use client';
import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { DialogTrigger } from '@radix-ui/react-dialog';
import { Plus } from 'lucide-react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';

import { addMemberFormSchema } from '@/@types';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import useDebounce from '@/hooks/use-debounce';
import { useAddMember } from '@/hooks/use-member';
import { useOrganizations } from '@/hooks/use-organizations';
import { useRecruiters } from '@/hooks/use-recruiters';
import { cn, getOrgAbbreviation } from '@/lib/utils';

// const roles = [
//   { value: 'admin', label: 'Admin' },
//   { value: 'manager', label: 'Manager' },
//   { value: 'member', label: 'Member' },
//   // ...add more roles as needed
// ];

export default function AddMemberForm({ organizationId }: { organizationId: string }) {
  // Initialize the form with validation schema
  const [open, setOpen] = useState(false);
  const form = useForm({
    resolver: zodResolver(addMemberFormSchema),
    defaultValues: {
      userId: '',
      organizationId: organizationId,
      roles: ['member'] as ('admin' | 'member' | 'owner')[],
    },
  });
  const [orgSearchQuery, setOrgSearchQuery] = useState('');
  const [reqSearchQuery, setReqSearchQuery] = useState('');
  const searchOrgDebounced = useDebounce(orgSearchQuery, 500);
  const searchRecDebounced = useDebounce(reqSearchQuery, 500);

  const { data: organizationsData, isLoading: isLoadingOrganizations } = useOrganizations({
    search: searchOrgDebounced,
    checkAdmin: true,
    organizationId,
    limit: 100,
  });

  const { data: recruitersData, isLoading: isLoadingRecruiters } = useRecruiters({
    search: searchRecDebounced,
    limit: 100,
  });

  const organizations = useMemo(() => {
    return organizationsData?.data || [];
  }, [organizationsData]);

  const recruiters = useMemo(() => {
    return recruitersData?.data || [];
  }, [recruitersData]);

  const addMemberMutation = useAddMember();

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {/* Move the trigger button to the right and use a primary color */}
      <div className="flex justify-end mb-4">
        <DialogTrigger asChild>
          <Button variant="default" size="sm" className="bg-primary text-white">
            <Plus className="mr-2 h-4 w-4" />
            Add Member
          </Button>
        </DialogTrigger>
      </div>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add Member</DialogTitle>
          <DialogDescription>Add a new member to the organization</DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(async (data) => {
              console.log(data);
              await addMemberMutation.mutateAsync({
                organizationId: data.organizationId,
                userId: data.userId,
                roles: data.roles,
              });
              setOpen(false); // Optionally close dialog on submit
            })}
          >
            <FormField
              control={form.control}
              name="organizationId"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Organization *</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          className={cn(
                            'w-full justify-between',
                            !field.value && 'text-muted-foreground'
                          )}
                        >
                          {field.value ? (
                            <div className="flex items-center gap-2">
                              <Avatar className="w-5 h-5">
                                <AvatarFallback className="text-xs font-medium">
                                  {getOrgAbbreviation(
                                    organizations.find((org) => org.id === field.value)?.name || ''
                                  )}
                                </AvatarFallback>
                              </Avatar>
                              <span className="truncate">
                                {organizations.find((org) => org.id === field.value)?.name}
                              </span>
                            </div>
                          ) : (
                            'Select organization'
                          )}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" align="start">
                      <Command>
                        <CommandInput
                          placeholder="Search organizations..."
                          value={orgSearchQuery}
                          onValueChange={setOrgSearchQuery}
                        />
                        <CommandList>
                          <CommandEmpty>
                            {isLoadingOrganizations ? 'Loading...' : 'No organization found.'}
                          </CommandEmpty>
                          <CommandGroup>
                            <ScrollArea className="h-48">
                              {organizations?.map((organization) => (
                                <CommandItem
                                  value={organization.name}
                                  key={organization.id}
                                  onSelect={() => {
                                    form.setValue('organizationId', organization.id);
                                    setOrgSearchQuery('');
                                  }}
                                  className="flex items-center gap-3 p-3"
                                >
                                  <Check
                                    className={cn(
                                      'h-4 w-4 shrink-0',
                                      organization.id === field.value ? 'opacity-100' : 'opacity-0'
                                    )}
                                  />
                                  <div className="flex items-center gap-3 flex-1 min-w-0">
                                    <Avatar className="w-8 h-8 shrink-0">
                                      <AvatarFallback className="text-sm font-medium">
                                        {getOrgAbbreviation(organization.name)}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div className="flex flex-col min-w-0 flex-1">
                                      <span className="font-medium text-foreground truncate">
                                        {organization.name}
                                      </span>
                                      <span className="text-sm text-muted-foreground truncate">
                                        @{organization.slug}
                                      </span>
                                    </div>
                                  </div>
                                </CommandItem>
                              ))}
                            </ScrollArea>
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="userId"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Recruiter *</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          className={cn(
                            'w-full justify-between',
                            !field.value && 'text-muted-foreground'
                          )}
                        >
                          {field.value ? (
                            <div className="flex items-center gap-2">
                              <Avatar className="w-5 h-5">
                                <AvatarFallback className="text-xs font-medium">
                                  {getOrgAbbreviation(
                                    recruiters.find((org) => org.id === field.value)?.name || ''
                                  )}
                                </AvatarFallback>
                              </Avatar>
                              <span className="truncate">
                                {recruiters.find((org) => org.id === field.value)?.name}
                              </span>
                            </div>
                          ) : (
                            'Select Recruiter'
                          )}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" align="start">
                      <Command>
                        <CommandInput
                          placeholder="Search Recruiters..."
                          value={reqSearchQuery}
                          onValueChange={setReqSearchQuery}
                        />
                        <CommandList>
                          <CommandEmpty>
                            {isLoadingRecruiters ? 'Loading...' : 'No Recruiter found.'}
                          </CommandEmpty>
                          <CommandGroup>
                            <ScrollArea className="h-48">
                              {recruiters?.map((recruiter) => (
                                <CommandItem
                                  value={recruiter.name}
                                  key={recruiter.id}
                                  onSelect={() => {
                                    form.setValue('userId', recruiter.id);
                                    setReqSearchQuery('');
                                  }}
                                  className="flex items-center gap-3 p-3"
                                >
                                  <Check
                                    className={cn(
                                      'h-4 w-4 shrink-0',
                                      recruiter.id === field.value ? 'opacity-100' : 'opacity-0'
                                    )}
                                  />
                                  <div className="flex items-center gap-3 flex-1 min-w-0">
                                    <Avatar className="w-8 h-8 shrink-0">
                                      <AvatarFallback className="text-sm font-medium">
                                        {getOrgAbbreviation(recruiter.name)}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div className="flex flex-col min-w-0 flex-1">
                                      <span className="font-medium text-foreground truncate">
                                        {recruiter.name}
                                      </span>
                                      <span className="text-sm text-muted-foreground truncate">
                                        {recruiter.email}
                                      </span>
                                    </div>
                                  </div>
                                </CommandItem>
                              ))}
                            </ScrollArea>
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            {/* <FormField
              control={form.control}
              name="roles"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Role(s) *</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          className={cn(
                            'w-full justify-between',
                            field.value?.length === 0 && 'text-muted-foreground'
                          )}
                        >
                          {field.value?.length > 0
                            ? `${field.value.length} role${field.value.length > 1 ? 's' : ''} selected`
                            : 'Select roles'}
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0" align="start">
                      <Command>
                        <CommandInput placeholder="Search roles..." className="h-9" />
                        <CommandList>
                          <CommandEmpty>No roles found.</CommandEmpty>
                          <CommandGroup>
                            <ScrollArea className="h-32">
                              {roles.map((role) => (
                                <CommandItem
                                  value={role.label}
                                  key={role.value}
                                  onSelect={() => {
                                    const currentRoles = field.value || [];
                                    if (currentRoles.includes(role.value)) {
                                      // Remove role
                                      field.onChange(currentRoles.filter((r) => r !== role.value));
                                    } else {
                                      // Add role
                                      field.onChange([...currentRoles, role.value]);
                                    }
                                  }}
                                >
                                  <Check
                                    className={cn(
                                      'mr-2 h-4 w-4',
                                      field.value?.includes(role.value)
                                        ? 'opacity-100'
                                        : 'opacity-0'
                                    )}
                                  />
                                  {role.label}
                                </CommandItem>
                              ))}
                            </ScrollArea>
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  {field.value?.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {field.value.map((roleValue: string) => {
                        const role = roles.find((r) => r.value === roleValue);
                        return (
                          <Badge key={roleValue} variant="secondary" className="text-xs">
                            {role?.label || roleValue}
                            <button
                              type="button"
                              className="ml-1 hover:bg-muted-foreground/20 rounded-full"
                              onClick={() => {
                                field.onChange(field.value.filter((r: string) => r !== roleValue));
                              }}
                            >
                              <XIcon className="h-3 w-3" />
                            </button>
                          </Badge>
                        );
                      })}
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            /> */}
            <div className="flex justify-end gap-2 mt-6">
              <Button type="button" variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">Submit</Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
