import { Plus } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

import { AddOrganizationForm } from './_components/add-organization-form';
import { OrganizationsTable } from './_components/organizations-table';

export default function OrganizationsPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Organizations</h1>
          <p className="text-muted-foreground">
            Manage client organizations and their hiring needs
          </p>
        </div>
        <AddOrganizationForm
          trigger={
            <Button>
              <Plus className=" mr-2 h-4 w-4" />
              Add Organization
            </Button>
          }
        />
      </div>

      {/* Organizations Table */}
      <Card>
        <CardContent>
          <OrganizationsTable />
        </CardContent>
      </Card>
    </div>
  );
}
