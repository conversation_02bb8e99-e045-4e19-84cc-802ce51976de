'use client';

import {
  IconBook,
  IconExternalLink,
  IconHelp,
  IconMail,
  IconMessageCircle,
  IconPhone,
  IconSearch,
  IconVideo,
} from '@tabler/icons-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

import { useRole } from '../../../../../../contexts/role-context';

export function HelpContent() {
  const { role } = useRole();

  const helpCategories = [
    {
      title: 'Getting Started',
      description: 'Learn the basics of using the platform',
      icon: IconBook,
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10',
      articles: ['Platform Overview', 'Account Setup', 'First Steps'],
    },
    {
      title: 'Account & Profile',
      description: 'Manage your account and profile settings',
      icon: IconHelp,
      color: 'text-green-500',
      bgColor: 'bg-green-500/10',
      articles: ['Profile Setup', 'Privacy Settings', 'Account Security'],
    },
    {
      title: 'Communication',
      description: 'Connect with support and community',
      icon: IconMessageCircle,
      color: 'text-purple-500',
      bgColor: 'bg-purple-500/10',
      articles: ['Contact Support', 'Community Forums', 'Feedback'],
    },
  ];

  const roleSpecificHelp = {
    admin: [
      { title: 'User Management', description: 'Managing users, roles, and permissions' },
      { title: 'System Configuration', description: 'Platform settings and customization' },
      { title: 'Analytics & Reports', description: 'Understanding platform metrics' },
      { title: 'Organization Setup', description: 'Setting up multiple organizations' },
    ],
    recruiter: [
      { title: 'Job Posting', description: 'Creating and managing job listings' },
      { title: 'Candidate Management', description: 'Screening and managing candidates' },
      { title: 'Interview Scheduling', description: 'Setting up interviews and assessments' },
      { title: 'Recruitment Analytics', description: 'Tracking recruitment performance' },
    ],
    candidate: [
      { title: 'Job Search', description: 'Finding and applying for positions' },
      { title: 'Profile Optimization', description: 'Making your profile stand out' },
      { title: 'Interview Preparation', description: 'Preparing for AI-powered interviews' },
      { title: 'Application Tracking', description: 'Managing your applications' },
    ],
  };

  const contactOptions = [
    {
      title: 'Email Support',
      description: 'Get help via email within 24 hours',
      icon: IconMail,
      contact: '<EMAIL>',
      color: 'text-blue-500',
    },
    {
      title: 'Live Chat',
      description: 'Chat with our support team instantly',
      icon: IconMessageCircle,
      contact: 'Available 9 AM - 6 PM EST',
      color: 'text-green-500',
    },
    {
      title: 'Phone Support',
      description: 'Call us for urgent issues',
      icon: IconPhone,
      contact: '+1 (555) HIRE-123',
      color: 'text-orange-500',
    },
    {
      title: 'Video Call',
      description: 'Schedule a screen sharing session',
      icon: IconVideo,
      contact: 'Schedule a call',
      color: 'text-purple-500',
    },
  ];

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Header */}
      <div className="space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">Help & Support</h2>
        <p className="text-muted-foreground">
          Get help and learn how to make the most of the platform
        </p>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="relative">
            <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search help articles, guides, and FAQs..." className="pl-10" />
          </div>
        </CardContent>
      </Card>

      {/* Role Badge */}
      <div className="flex items-center gap-2">
        <Badge variant="outline" className="capitalize">
          {role} Help Center
        </Badge>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {contactOptions.map((option, index) => (
          <Card
            key={index}
            className="relative overflow-hidden transition-all duration-200 hover:shadow-md group cursor-pointer"
          >
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-md bg-muted/50`}>
                  <option.icon className={`h-5 w-5 ${option.color}`} />
                </div>
                <div>
                  <h4 className="font-medium">{option.title}</h4>
                  <p className="text-sm text-muted-foreground">{option.description}</p>
                  <p className="text-sm font-medium mt-1">{option.contact}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {/* Help Categories */}
        <Card>
          <CardHeader>
            <CardTitle>Help Categories</CardTitle>
            <CardDescription>Browse help topics by category</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {helpCategories.map((category, index) => (
              <div
                key={index}
                className="p-4 rounded-lg border transition-colors hover:bg-muted/50 cursor-pointer"
              >
                <div className="flex items-start gap-3">
                  <div className={`p-2 rounded-md ${category.bgColor}`}>
                    <category.icon className={`h-5 w-5 ${category.color}`} />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium">{category.title}</h4>
                    <p className="text-sm text-muted-foreground mb-2">{category.description}</p>
                    <div className="flex flex-wrap gap-1">
                      {category.articles.map((article, articleIndex) => (
                        <Badge key={articleIndex} variant="outline" className="text-xs">
                          {article}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <IconExternalLink className="h-4 w-4 text-muted-foreground" />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Role-specific Help */}
        <Card>
          <CardHeader>
            <CardTitle className="capitalize">{role} Resources</CardTitle>
            <CardDescription>Help topics specific to your role</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {roleSpecificHelp[role]?.map((item, index) => (
              <div
                key={index}
                className="p-3 rounded-lg bg-muted/50 hover:bg-muted transition-colors cursor-pointer"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{item.title}</h4>
                    <p className="text-sm text-muted-foreground">{item.description}</p>
                  </div>
                  <IconExternalLink className="h-4 w-4 text-muted-foreground" />
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle>Frequently Asked Questions</CardTitle>
          <CardDescription>Common questions and answers</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="p-4 rounded-lg border">
              <h4 className="font-medium mb-2">How do I reset my password?</h4>
              <p className="text-sm text-muted-foreground">
                Go to the login page and click &quot;Forgot Password&quot;. Enter your email address
                and follow the instructions in the email you receive.
              </p>
            </div>

            <div className="p-4 rounded-lg border">
              <h4 className="font-medium mb-2">How do I update my profile information?</h4>
              <p className="text-sm text-muted-foreground">
                Navigate to Settings {'>'} Profile and click the Edit button. Make your changes and
                click Save Changes to update your information.
              </p>
            </div>

            {role === 'candidate' && (
              <div className="p-4 rounded-lg border">
                <h4 className="font-medium mb-2">How do I prepare for an AI interview?</h4>
                <p className="text-sm text-muted-foreground">
                  Review the job description, practice common interview questions, ensure you have a
                  stable internet connection, and test your camera and microphone beforehand.
                </p>
              </div>
            )}

            {(role === 'admin' || role === 'recruiter') && (
              <div className="p-4 rounded-lg border">
                <h4 className="font-medium mb-2">How do I post a new job?</h4>
                <p className="text-sm text-muted-foreground">
                  Go to Jobs &gt; Create New Job. Fill in the job details, requirements, and click
                  Publish. The job will be reviewed and published within 24 hours.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Contact Support */}
      <Card>
        <CardHeader>
          <CardTitle>Still Need Help?</CardTitle>
          <CardDescription>Contact our support team for personalized assistance</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button className="flex-1">
              <IconMail className="h-4 w-4 mr-2" />
              Email Support
            </Button>
            <Button variant="outline" className="flex-1">
              <IconMessageCircle className="h-4 w-4 mr-2" />
              Start Live Chat
            </Button>
            <Button variant="outline" className="flex-1">
              <IconPhone className="h-4 w-4 mr-2" />
              Request Call Back
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
