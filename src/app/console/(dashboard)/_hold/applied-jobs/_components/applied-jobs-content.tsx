'use client';

import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Eye,
  Filter,
  MapPin,
  MessageSquare,
  Search,
  TrendingUp,
  XCircle,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { useRole } from '../../../../../../contexts/role-context';

// Mock data for applied jobs
const mockAppliedJobs = [
  {
    id: 1,
    jobTitle: 'Senior Frontend Developer',
    company: 'TechCorp Inc.',
    appliedDate: '2024-01-20',
    status: 'Under Review',
    stage: 'Technical Interview',
    location: 'San Francisco, CA',
    salary: '$120k - $150k',
    type: 'Full-time',
    applicationId: 'APP-2024-001',
    progress: 60,
    nextStep: 'Technical interview scheduled for Jan 25',
    urgency: 'medium',
  },
  {
    id: 2,
    jobTitle: 'Full Stack Engineer',
    company: 'StartupXYZ',
    appliedDate: '2024-01-18',
    status: 'Shortlisted',
    stage: 'Final Interview',
    location: 'Remote',
    salary: '$100k - $130k',
    type: 'Full-time',
    applicationId: 'APP-2024-002',
    progress: 80,
    nextStep: 'Final interview with CTO on Jan 26',
    urgency: 'high',
  },
  {
    id: 3,
    jobTitle: 'React Developer',
    company: 'Digital Agency',
    appliedDate: '2024-01-15',
    status: 'Rejected',
    stage: 'HR Screening',
    location: 'New York, NY',
    salary: '$80k - $100k',
    type: 'Contract',
    applicationId: 'APP-2024-003',
    progress: 25,
    nextStep: 'Application closed',
    urgency: 'low',
    feedback: 'Great technical skills, but looking for more experience with specific frameworks.',
  },
  {
    id: 4,
    jobTitle: 'Product Manager',
    company: 'GrowthCo',
    appliedDate: '2024-01-22',
    status: 'New',
    stage: 'Application Review',
    location: 'Austin, TX',
    salary: '$110k - $140k',
    type: 'Full-time',
    applicationId: 'APP-2024-004',
    progress: 10,
    nextStep: 'Initial review in progress',
    urgency: 'low',
  },
  {
    id: 5,
    jobTitle: 'Senior UI/UX Designer',
    company: 'DesignStudio',
    appliedDate: '2024-01-19',
    status: 'Interview Scheduled',
    stage: 'Portfolio Review',
    location: 'Los Angeles, CA',
    salary: '$95k - $120k',
    type: 'Full-time',
    applicationId: 'APP-2024-005',
    progress: 40,
    nextStep: 'Portfolio presentation on Jan 24',
    urgency: 'high',
  },
];

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'new':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'under review':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    case 'interview scheduled':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
    case 'shortlisted':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case 'rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    case 'hired':
      return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
};

const getStatusIcon = (status: string) => {
  switch (status.toLowerCase()) {
    case 'shortlisted':
    case 'hired':
      return <CheckCircle className="h-4 w-4" />;
    case 'rejected':
      return <XCircle className="h-4 w-4" />;
    case 'interview scheduled':
      return <Calendar className="h-4 w-4" />;
    default:
      return <Clock className="h-4 w-4" />;
  }
};

const getUrgencyIcon = (urgency: string) => {
  switch (urgency) {
    case 'high':
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    case 'medium':
      return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    default:
      return null;
  }
};

export function AppliedJobsContent() {
  const { role } = useRole();

  // Redirect non-candidates away from applied jobs
  if (role !== 'candidate') {
    return (
      <>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <div className="text-center">
            <h2 className="text-2xl font-bold">Access Restricted</h2>
            <p className="text-muted-foreground">Applied jobs are only available to candidates.</p>
          </div>
        </div>
      </>
    );
  }

  const activeApplications = mockAppliedJobs.filter((job) => job.status !== 'Rejected');
  const rejectedApplications = mockAppliedJobs.filter((job) => job.status === 'Rejected');

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Applied Jobs</h1>
            <p className="text-muted-foreground">Track and manage your job applications</p>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockAppliedJobs.length}</div>
              <p className="text-xs text-muted-foreground">+2 this week</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Applications</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activeApplications.length}</div>
              <p className="text-xs text-muted-foreground">In progress</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Interviews Scheduled</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {
                  mockAppliedJobs.filter(
                    (job) => job.status === 'Interview Scheduled' || job.status === 'Shortlisted'
                  ).length
                }
              </div>
              <p className="text-xs text-muted-foreground">Upcoming</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Response Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">80%</div>
              <p className="text-xs text-muted-foreground">Companies responding</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input placeholder="Search applications..." className="pl-10" />
          </div>
          <Select defaultValue="all">
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="interview">Interview Stage</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="all">
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="Company" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Companies</SelectItem>
              <SelectItem value="techcorp">TechCorp Inc.</SelectItem>
              <SelectItem value="startupxyz">StartupXYZ</SelectItem>
              <SelectItem value="digital">Digital Agency</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>

        {/* Applications Tabs */}
        <Tabs defaultValue="active" className="w-full">
          <TabsList>
            <TabsTrigger value="active">
              Active Applications ({activeApplications.length})
            </TabsTrigger>
            <TabsTrigger value="rejected">Rejected ({rejectedApplications.length})</TabsTrigger>
            <TabsTrigger value="all">All Applications ({mockAppliedJobs.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="active" className="space-y-4 mt-6">
            {activeApplications.map((application) => (
              <Card key={application.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="text-lg font-semibold">{application.jobTitle}</h3>
                            {getUrgencyIcon(application.urgency)}
                          </div>
                          <p className="text-muted-foreground">{application.company}</p>
                        </div>
                        <Badge className={getStatusColor(application.status)}>
                          {getStatusIcon(application.status)}
                          <span className="ml-1">{application.status}</span>
                        </Badge>
                      </div>

                      <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Applied {application.appliedDate}
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {application.location}
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          {application.salary}
                        </div>
                        <div>App ID: {application.applicationId}</div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{application.type}</Badge>
                        <Badge variant="outline">Stage: {application.stage}</Badge>
                      </div>

                      {/* Progress Bar */}
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Application Progress</span>
                          <span>{application.progress}%</span>
                        </div>
                        <div className="w-full bg-muted rounded-full h-2">
                          <div
                            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${application.progress}%` }}
                          ></div>
                        </div>
                      </div>

                      {/* Next Step */}
                      <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
                        <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                          Next Step:
                        </p>
                        <p className="text-sm text-blue-800 dark:text-blue-200">
                          {application.nextStep}
                        </p>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 md:ml-4">
                      <Button>
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Button>
                      <Button variant="outline" size="sm">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Contact HR
                      </Button>
                      {application.urgency === 'high' && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-red-200 text-red-600 hover:bg-red-50"
                        >
                          <AlertCircle className="mr-2 h-4 w-4" />
                          Action Required
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {activeApplications.length === 0 && (
              <Card>
                <CardContent className="p-6 text-center">
                  <Clock className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No active applications</h3>
                  <p className="text-muted-foreground">
                    Your active job applications will appear here.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="rejected" className="space-y-4 mt-6">
            {rejectedApplications.map((application) => (
              <Card key={application.id} className="hover:shadow-md transition-shadow opacity-75">
                <CardContent className="p-6">
                  <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-lg font-semibold">{application.jobTitle}</h3>
                          <p className="text-muted-foreground">{application.company}</p>
                        </div>
                        <Badge className={getStatusColor(application.status)}>
                          {getStatusIcon(application.status)}
                          <span className="ml-1">{application.status}</span>
                        </Badge>
                      </div>

                      <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Applied {application.appliedDate}
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {application.location}
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          {application.salary}
                        </div>
                        <div>App ID: {application.applicationId}</div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{application.type}</Badge>
                        <Badge variant="outline">Stage: {application.stage}</Badge>
                      </div>

                      {/* Feedback */}
                      {application.feedback && (
                        <div className="bg-red-50 dark:bg-red-950 p-3 rounded-lg">
                          <p className="text-sm font-medium text-red-900 dark:text-red-100">
                            Feedback:
                          </p>
                          <p className="text-sm text-red-800 dark:text-red-200">
                            {application.feedback}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col gap-2 md:ml-4">
                      <Button variant="outline" size="sm">
                        <Eye className="mr-2 h-4 w-4" />
                        View Feedback
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="all" className="space-y-4 mt-6">
            {mockAppliedJobs.map((application) => (
              <Card
                key={application.id}
                className={`hover:shadow-md transition-shadow ${application.status === 'Rejected' ? 'opacity-75' : ''}`}
              >
                <CardContent className="p-6">
                  <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="text-lg font-semibold">{application.jobTitle}</h3>
                            {application.status !== 'Rejected' &&
                              getUrgencyIcon(application.urgency)}
                          </div>
                          <p className="text-muted-foreground">{application.company}</p>
                        </div>
                        <Badge className={getStatusColor(application.status)}>
                          {getStatusIcon(application.status)}
                          <span className="ml-1">{application.status}</span>
                        </Badge>
                      </div>

                      <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Applied {application.appliedDate}
                        </div>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {application.location}
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          {application.salary}
                        </div>
                        <div>App ID: {application.applicationId}</div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{application.type}</Badge>
                        <Badge variant="outline">Stage: {application.stage}</Badge>
                      </div>

                      {application.status !== 'Rejected' && (
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span>{application.progress}%</span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ width: `${application.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col gap-2 md:ml-4">
                      <Button variant="outline" size="sm">
                        <Eye className="mr-2 h-4 w-4" />
                        View Details
                      </Button>
                      {application.status !== 'Rejected' && (
                        <Button variant="outline" size="sm">
                          <MessageSquare className="mr-2 h-4 w-4" />
                          Contact HR
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
