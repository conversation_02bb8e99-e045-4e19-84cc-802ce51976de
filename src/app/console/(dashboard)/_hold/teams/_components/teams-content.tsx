'use client';

import {
  Crown,
  Edit,
  Filter,
  Mail,
  MoreHorizontal,
  Phone,
  Plus,
  Search,
  Settings,
  Shield,
  User,
  UserPlus,
  Users,
} from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { useRole } from '../../../../../../contexts/role-context';

// Mock data for teams
const mockTeams = {
  admin: [
    {
      id: 1,
      name: 'Engineering Team',
      department: 'Engineering',
      description: 'Core engineering team responsible for product development',
      lead: {
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: '',
        role: 'Engineering Manager',
      },
      members: [
        {
          id: 1,
          name: '<PERSON>',
          email: '<EMAIL>',
          avatar: '',
          role: 'Engineering Manager',
          type: 'Lead',
          department: 'Engineering',
          joinDate: '2022-01-15',
          status: 'Active',
        },
        {
          id: 2,
          name: 'Mike Chen',
          email: '<EMAIL>',
          avatar: '',
          role: 'Senior Developer',
          type: 'Member',
          department: 'Engineering',
          joinDate: '2022-03-20',
          status: 'Active',
        },
        {
          id: 3,
          name: 'Emily Davis',
          email: '<EMAIL>',
          avatar: '',
          role: 'Frontend Developer',
          type: 'Member',
          department: 'Engineering',
          joinDate: '2023-01-10',
          status: 'Active',
        },
        {
          id: 4,
          name: 'Alex Wilson',
          email: '<EMAIL>',
          avatar: '',
          role: 'DevOps Engineer',
          type: 'Member',
          department: 'Engineering',
          joinDate: '2023-06-01',
          status: 'Active',
        },
      ],
      activeProjects: 8,
      totalMembers: 4,
      openPositions: 3,
      status: 'Active',
    },
    {
      id: 2,
      name: 'Product Team',
      department: 'Product',
      description: 'Product strategy and user experience team',
      lead: {
        name: 'Lisa Brown',
        email: '<EMAIL>',
        avatar: '',
        role: 'Product Manager',
      },
      members: [
        {
          id: 5,
          name: 'Lisa Brown',
          email: '<EMAIL>',
          avatar: '',
          role: 'Product Manager',
          type: 'Lead',
          department: 'Product',
          joinDate: '2021-08-15',
          status: 'Active',
        },
        {
          id: 6,
          name: 'David Kim',
          email: '<EMAIL>',
          avatar: '',
          role: 'UX Designer',
          type: 'Member',
          department: 'Product',
          joinDate: '2022-11-01',
          status: 'Active',
        },
        {
          id: 7,
          name: 'Jessica Liu',
          email: '<EMAIL>',
          avatar: '',
          role: 'Product Analyst',
          type: 'Member',
          department: 'Product',
          joinDate: '2023-04-15',
          status: 'Active',
        },
      ],
      activeProjects: 5,
      totalMembers: 3,
      openPositions: 2,
      status: 'Active',
    },
    {
      id: 3,
      name: 'Marketing Team',
      department: 'Marketing',
      description: 'Digital marketing and brand management team',
      lead: {
        name: 'Ryan Garcia',
        email: '<EMAIL>',
        avatar: '',
        role: 'Marketing Director',
      },
      members: [
        {
          id: 8,
          name: 'Ryan Garcia',
          email: '<EMAIL>',
          avatar: '',
          role: 'Marketing Director',
          type: 'Lead',
          department: 'Marketing',
          joinDate: '2021-05-10',
          status: 'Active',
        },
        {
          id: 9,
          name: 'Amanda Taylor',
          email: '<EMAIL>',
          avatar: '',
          role: 'Content Manager',
          type: 'Member',
          department: 'Marketing',
          joinDate: '2022-09-01',
          status: 'Active',
        },
      ],
      activeProjects: 3,
      totalMembers: 2,
      openPositions: 1,
      status: 'Active',
    },
  ],
  recruiter: [
    {
      id: 1,
      name: 'Engineering Team',
      department: 'Engineering',
      description: 'Core engineering team responsible for product development',
      lead: {
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        avatar: '',
        role: 'Engineering Manager',
      },
      members: [
        {
          id: 1,
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          avatar: '',
          role: 'Engineering Manager',
          type: 'Lead',
          department: 'Engineering',
          joinDate: '2022-01-15',
          status: 'Active',
        },
        {
          id: 2,
          name: 'Mike Chen',
          email: '<EMAIL>',
          avatar: '',
          role: 'Senior Developer',
          type: 'Member',
          department: 'Engineering',
          joinDate: '2022-03-20',
          status: 'Active',
        },
        {
          id: 3,
          name: 'Emily Davis',
          email: '<EMAIL>',
          avatar: '',
          role: 'Frontend Developer',
          type: 'Member',
          department: 'Engineering',
          joinDate: '2023-01-10',
          status: 'Active',
        },
      ],
      activeProjects: 8,
      totalMembers: 3,
      openPositions: 3,
      status: 'Active',
    },
  ],
};

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'active':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case 'inactive':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    case 'on-leave':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    default:
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
  }
};

const getRoleIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case 'lead':
      return <Crown className="h-4 w-4 text-yellow-500" />;
    case 'admin':
      return <Shield className="h-4 w-4 text-blue-500" />;
    default:
      return <User className="h-4 w-4 text-gray-500" />;
  }
};

export function TeamsContent() {
  const { role } = useRole();

  // Redirect candidates away from teams
  if (role === 'candidate') {
    return (
      <>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <div className="text-center">
            <h2 className="text-2xl font-bold">Access Restricted</h2>
            <p className="text-muted-foreground">
              Teams are only available to admin and recruiter users.
            </p>
          </div>
        </div>
      </>
    );
  }

  const teams = mockTeams[role] || [];
  const allMembers = teams.flatMap((team) => team.members);

  const renderTeamsView = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Teams</h1>
          <p className="text-muted-foreground">
            {role === 'admin'
              ? 'Manage teams and team members across departments'
              : "View your organization's teams and members"}
          </p>
        </div>
        {role === 'admin' && (
          <div className="flex gap-2">
            <Button variant="outline">
              <UserPlus className="mr-2 h-4 w-4" />
              Invite Member
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Team
            </Button>
          </div>
        )}
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input placeholder="Search teams or members..." className="pl-10" />
        </div>
        <Select defaultValue="all">
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="Department" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Departments</SelectItem>
            <SelectItem value="engineering">Engineering</SelectItem>
            <SelectItem value="product">Product</SelectItem>
            <SelectItem value="marketing">Marketing</SelectItem>
            <SelectItem value="design">Design</SelectItem>
          </SelectContent>
        </Select>
        <Select defaultValue="all">
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Teams</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{teams.length}</div>
            <p className="text-xs text-muted-foreground">Active teams</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{allMembers.length}</div>
            <p className="text-xs text-muted-foreground">Team members</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Open Positions</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {teams.reduce((sum, team) => sum + team.openPositions, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Hiring needs</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {teams.reduce((sum, team) => sum + team.activeProjects, 0)}
            </div>
            <p className="text-xs text-muted-foreground">In progress</p>
          </CardContent>
        </Card>
      </div>

      {/* Teams Content */}
      <Tabs defaultValue="teams" className="w-full">
        <TabsList>
          <TabsTrigger value="teams">Teams ({teams.length})</TabsTrigger>
          <TabsTrigger value="members">All Members ({allMembers.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="teams" className="space-y-4 mt-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {teams.map((team) => (
              <Card key={team.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-lg">{team.name}</CardTitle>
                      <CardDescription>{team.department}</CardDescription>
                    </div>
                    {role === 'admin' && (
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground line-clamp-2">{team.description}</p>

                  {/* Team Lead */}
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Team Lead</div>
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={team.lead.avatar} />
                        <AvatarFallback>
                          {team.lead.name
                            .split(' ')
                            .map((n) => n[0])
                            .join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium">{team.lead.name}</div>
                        <div className="text-xs text-muted-foreground">{team.lead.role}</div>
                      </div>
                    </div>
                  </div>

                  {/* Team Stats */}
                  <div className="grid grid-cols-3 gap-4 text-center text-sm">
                    <div>
                      <div className="font-semibold">{team.totalMembers}</div>
                      <div className="text-muted-foreground">Members</div>
                    </div>
                    <div>
                      <div className="font-semibold">{team.activeProjects}</div>
                      <div className="text-muted-foreground">Projects</div>
                    </div>
                    <div>
                      <div className="font-semibold">{team.openPositions}</div>
                      <div className="text-muted-foreground">Open Roles</div>
                    </div>
                  </div>

                  {/* Team Members Preview */}
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Members</div>
                    <div className="flex -space-x-2">
                      {team.members.slice(0, 4).map((member, index) => (
                        <Avatar key={index} className="h-8 w-8 border-2 border-background">
                          <AvatarImage src={member.avatar} />
                          <AvatarFallback className="text-xs">
                            {member.name
                              .split(' ')
                              .map((n) => n[0])
                              .join('')}
                          </AvatarFallback>
                        </Avatar>
                      ))}
                      {team.members.length > 4 && (
                        <div className="h-8 w-8 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs font-medium">
                          +{team.members.length - 4}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Button size="sm" className="flex-1">
                      View Team
                    </Button>
                    {role === 'admin' && (
                      <Button variant="outline" size="sm">
                        <Settings className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="members" className="space-y-4 mt-6">
          <div className="space-y-4">
            {allMembers.map((member) => (
              <Card key={member.id}>
                <CardContent className="p-6">
                  <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={member.avatar} />
                        <AvatarFallback>
                          {member.name
                            .split(' ')
                            .map((n) => n[0])
                            .join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <h3 className="text-lg font-semibold">{member.name}</h3>
                          {getRoleIcon(member.type)}
                          <Badge className={getStatusColor(member.status)}>{member.status}</Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{member.email}</p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>{member.role}</span>
                          <span>•</span>
                          <span>{member.department}</span>
                          <span>•</span>
                          <span>Joined {member.joinDate}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Mail className="mr-2 h-4 w-4" />
                        Email
                      </Button>
                      <Button variant="outline" size="sm">
                        <Phone className="mr-2 h-4 w-4" />
                        Call
                      </Button>
                      {role === 'admin' && (
                        <Button variant="outline" size="sm">
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );

  return <>{renderTeamsView()}</>;
}
