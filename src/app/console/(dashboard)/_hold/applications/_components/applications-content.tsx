'use client';

import { Download, Eye, MessageSquare } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function ApplicationsContent() {
  // Mock data - replace with actual data fetching
  const applications = [
    {
      id: 1,
      candidateName: '<PERSON>',
      position: 'Frontend Developer',
      status: 'pending',
      appliedDate: '2024-01-15',
      email: '<EMAIL>',
    },
    {
      id: 2,
      candidateName: '<PERSON>',
      position: 'Backend Developer',
      status: 'reviewed',
      appliedDate: '2024-01-14',
      email: '<EMAIL>',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'reviewed':
        return 'bg-blue-100 text-blue-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Applications</h1>
          <div className="flex gap-2">
            <Button variant="outline">Filter</Button>
            <Button variant="outline">Export</Button>
          </div>
        </div>

        <div className="grid gap-4">
          {applications.map((application) => (
            <Card key={application.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{application.candidateName}</CardTitle>
                    <p className="text-sm text-muted-foreground">{application.email}</p>
                  </div>
                  <Badge className={getStatusColor(application.status)}>{application.status}</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">{application.position}</p>
                    <p className="text-sm text-muted-foreground">
                      Applied on {new Date(application.appliedDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <MessageSquare className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </>
  );
}
