'use client';

import {
  Activity,
  BarChart3,
  Calendar,
  Clock,
  DollarSign,
  Download,
  LineChart,
  Target,
  TrendingDown,
  TrendingUp,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { useRole } from '../../../../../../contexts/role-context';

export function AnalyticsContent() {
  const { role } = useRole();

  // Redirect candidates away from analytics
  if (role === 'candidate') {
    return (
      <>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <div className="text-center">
            <h2 className="text-2xl font-bold">Access Restricted</h2>
            <p className="text-muted-foreground">
              Analytics are only available to admin and recruiter users.
            </p>
          </div>
        </div>
      </>
    );
  }

  const renderAnalyticsView = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Deep insights into your recruitment data and trends
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            Custom Date Range
          </Button>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Export Data
          </Button>
        </div>
      </div>

      {/* Real-time Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-l-blue-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Live Applications</CardTitle>
            <Activity className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              +2 in last hour
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-l-green-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <Target className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">24.5%</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
              +5.2% this week
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-l-yellow-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.3h</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingDown className="mr-1 h-3 w-3 text-green-500" />
              -0.8h improved
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-l-purple-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cost per Hire</CardTitle>
            <DollarSign className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$2,450</div>
            <p className="text-xs text-muted-foreground flex items-center">
              <TrendingDown className="mr-1 h-3 w-3 text-green-500" />
              -$320 this month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="trends" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="predictive">Predictive</TabsTrigger>
          <TabsTrigger value="quality">Quality</TabsTrigger>
          <TabsTrigger value="efficiency">Efficiency</TabsTrigger>
          <TabsTrigger value="benchmarks">Benchmarks</TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-6 mt-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Application Volume Trend
                </CardTitle>
                <CardDescription>Daily application volume over the last 90 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/10 rounded-lg">
                  <div className="text-center">
                    <LineChart className="h-16 w-16 text-muted-foreground mx-auto mb-2" />
                    <span className="text-muted-foreground">
                      Interactive trend chart would go here
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Hiring Velocity
                </CardTitle>
                <CardDescription>Time to hire trends by department</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-muted/10 rounded-lg">
                  <div className="text-center">
                    <BarChart3 className="h-16 w-16 text-muted-foreground mx-auto mb-2" />
                    <span className="text-muted-foreground">Velocity chart would go here</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Department Trends</CardTitle>
              <CardDescription>Comparative analysis across departments</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">+18%</div>
                    <div className="text-sm text-muted-foreground">Engineering</div>
                    <div className="text-xs text-blue-600">Application growth</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 dark:bg-green-950 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">+12%</div>
                    <div className="text-sm text-muted-foreground">Product</div>
                    <div className="text-xs text-green-600">Quality improvement</div>
                  </div>
                  <div className="text-center p-4 bg-yellow-50 dark:bg-yellow-950 rounded-lg">
                    <div className="text-2xl font-bold text-yellow-600">-3 days</div>
                    <div className="text-sm text-muted-foreground">Design</div>
                    <div className="text-xs text-yellow-600">Faster hiring</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-950 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">+25%</div>
                    <div className="text-sm text-muted-foreground">Marketing</div>
                    <div className="text-xs text-purple-600">Success rate</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="predictive" className="space-y-6 mt-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Hiring Forecast</CardTitle>
                <CardDescription>Predicted hiring needs for next quarter</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
                    <div>
                      <div className="font-semibold">Engineering</div>
                      <div className="text-sm text-muted-foreground">Based on growth trends</div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-blue-600">8-12</div>
                      <div className="text-xs text-blue-600">positions</div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-950 rounded-lg">
                    <div>
                      <div className="font-semibold">Product</div>
                      <div className="text-sm text-muted-foreground">Expansion plans</div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-green-600">3-5</div>
                      <div className="text-xs text-green-600">positions</div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-yellow-50 dark:bg-yellow-950 rounded-lg">
                    <div>
                      <div className="font-semibold">Design</div>
                      <div className="text-sm text-muted-foreground">Project pipeline</div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-yellow-600">2-4</div>
                      <div className="text-xs text-yellow-600">positions</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Candidate Success Prediction</CardTitle>
                <CardDescription>AI-powered candidate scoring model</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-semibold">High Success Probability</div>
                      <div className="text-sm text-muted-foreground">85%+ match score</div>
                    </div>
                    <Badge className="bg-green-100 text-green-800">23 candidates</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-semibold">Medium Success Probability</div>
                      <div className="text-sm text-muted-foreground">70-84% match score</div>
                    </div>
                    <Badge className="bg-yellow-100 text-yellow-800">45 candidates</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-semibold">Low Success Probability</div>
                      <div className="text-sm text-muted-foreground">Below 70% match score</div>
                    </div>
                    <Badge className="bg-red-100 text-red-800">12 candidates</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="quality" className="space-y-6 mt-6">
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Source Quality</CardTitle>
                <CardDescription>Quality metrics by application source</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">LinkedIn</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div className="w-[92%] bg-green-500 h-2 rounded-full"></div>
                      </div>
                      <span className="text-sm font-semibold">92%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Referrals</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div className="w-[89%] bg-green-500 h-2 rounded-full"></div>
                      </div>
                      <span className="text-sm font-semibold">89%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Company Website</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div className="w-[76%] bg-yellow-500 h-2 rounded-full"></div>
                      </div>
                      <span className="text-sm font-semibold">76%</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Job Boards</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div className="w-[63%] bg-orange-500 h-2 rounded-full"></div>
                      </div>
                      <span className="text-sm font-semibold">63%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Interview Quality</CardTitle>
                <CardDescription>Interview-to-hire conversion rates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center p-4 bg-green-50 dark:bg-green-950 rounded-lg">
                    <div className="text-3xl font-bold text-green-600">68%</div>
                    <div className="text-sm text-muted-foreground">First Round Success</div>
                  </div>
                  <div className="text-center p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                    <div className="text-3xl font-bold text-blue-600">45%</div>
                    <div className="text-sm text-muted-foreground">Technical Round Success</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 dark:bg-purple-950 rounded-lg">
                    <div className="text-3xl font-bold text-purple-600">82%</div>
                    <div className="text-sm text-muted-foreground">Final Round Success</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Candidate Experience</CardTitle>
                <CardDescription>Feedback and satisfaction scores</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Overall Satisfaction</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div className="w-[87%] bg-green-500 h-2 rounded-full"></div>
                      </div>
                      <span className="text-sm font-semibold">4.4/5</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Process Clarity</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div className="w-[84%] bg-green-500 h-2 rounded-full"></div>
                      </div>
                      <span className="text-sm font-semibold">4.2/5</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Communication</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div className="w-[90%] bg-green-500 h-2 rounded-full"></div>
                      </div>
                      <span className="text-sm font-semibold">4.5/5</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Interview Experience</span>
                    <div className="flex items-center gap-2">
                      <div className="w-16 bg-muted rounded-full h-2">
                        <div className="w-[82%] bg-green-500 h-2 rounded-full"></div>
                      </div>
                      <span className="text-sm font-semibold">4.1/5</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="efficiency" className="space-y-6 mt-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Process Efficiency</CardTitle>
                <CardDescription>Time spent in each hiring stage</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <div className="font-semibold">Application Review</div>
                      <div className="text-sm text-muted-foreground">Initial screening</div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">1.8 days</div>
                      <div className="text-xs text-green-600">-0.5 improved</div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <div className="font-semibold">Interview Scheduling</div>
                      <div className="text-sm text-muted-foreground">Coordination time</div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">2.3 days</div>
                      <div className="text-xs text-red-600">+0.2 slower</div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center p-3 border rounded-lg">
                    <div>
                      <div className="font-semibold">Decision Making</div>
                      <div className="text-sm text-muted-foreground">Post-interview</div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold">1.2 days</div>
                      <div className="text-xs text-green-600">-0.8 improved</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Resource Utilization</CardTitle>
                <CardDescription>Recruiter and interviewer workload</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {role === 'admin' && (
                    <>
                      <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
                        <div>
                          <div className="font-semibold">Sarah Johnson</div>
                          <div className="text-sm text-blue-600">Senior Recruiter</div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold">92%</div>
                          <div className="text-xs text-muted-foreground">Utilization</div>
                        </div>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-950 rounded-lg">
                        <div>
                          <div className="font-semibold">Mike Wilson</div>
                          <div className="text-sm text-green-600">Recruiter</div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold">78%</div>
                          <div className="text-xs text-muted-foreground">Utilization</div>
                        </div>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-yellow-50 dark:bg-yellow-950 rounded-lg">
                        <div>
                          <div className="font-semibold">Lisa Brown</div>
                          <div className="text-sm text-yellow-600">Junior Recruiter</div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold">65%</div>
                          <div className="text-xs text-muted-foreground">Utilization</div>
                        </div>
                      </div>
                    </>
                  )}
                  {role === 'recruiter' && (
                    <div className="text-center p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                      <div className="text-3xl font-bold text-blue-600">85%</div>
                      <div className="text-sm text-muted-foreground">Your Current Utilization</div>
                      <div className="text-xs text-blue-600 mt-2">15 active cases</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="benchmarks" className="space-y-6 mt-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Industry Benchmarks</CardTitle>
                <CardDescription>How you compare to industry standards</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Time to Hire</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">14 days</span>
                      <Badge className="bg-green-100 text-green-800">22% faster</Badge>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Cost per Hire</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">$2,450</span>
                      <Badge className="bg-green-100 text-green-800">15% lower</Badge>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Offer Acceptance Rate</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">89%</span>
                      <Badge className="bg-blue-100 text-blue-800">5% higher</Badge>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Quality of Hire</span>
                    <div className="flex items-center gap-2">
                      <span className="text-sm">4.2/5</span>
                      <Badge className="bg-yellow-100 text-yellow-800">Average</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Goals</CardTitle>
                <CardDescription>Progress towards quarterly targets</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Hiring Target</span>
                      <span className="text-sm">25/30 hires</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="w-[83%] bg-blue-500 h-2 rounded-full"></div>
                    </div>
                    <div className="text-xs text-muted-foreground">83% complete</div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Time to Hire Goal</span>
                      <span className="text-sm">14/12 days</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="w-[86%] bg-green-500 h-2 rounded-full"></div>
                    </div>
                    <div className="text-xs text-muted-foreground">86% of target</div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Cost Efficiency</span>
                      <span className="text-sm">$2,450/$2,800</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div className="w-[88%] bg-green-500 h-2 rounded-full"></div>
                    </div>
                    <div className="text-xs text-muted-foreground">12% under budget</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );

  return <>{renderAnalyticsView()}</>;
}
