'use client';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Clock,
  Download,
  Filter,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Target,
  TrendingUp,
  Users,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { useRole } from '../../../../../../contexts/role-context';

export function ReportsContent() {
  const { role } = useRole();

  // Redirect candidates away from reports
  if (role === 'candidate') {
    return (
      <>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <div className="text-center">
            <h2 className="text-2xl font-bold">Access Restricted</h2>
            <p className="text-muted-foreground">
              Reports are only available to admin and recruiter users.
            </p>
          </div>
        </div>
      </>
    );
  }

  const renderReportsView = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Reports & Analytics</h1>
          <p className="text-muted-foreground">
            Track hiring performance and analyze recruitment metrics
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Filter className="mr-2 h-4 w-4" />
            Filters
          </Button>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Date Range Selector */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center">
        <Select defaultValue="last-30-days">
          <SelectTrigger className="w-full md:w-[200px]">
            <SelectValue placeholder="Date Range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="last-7-days">Last 7 days</SelectItem>
            <SelectItem value="last-30-days">Last 30 days</SelectItem>
            <SelectItem value="last-90-days">Last 90 days</SelectItem>
            <SelectItem value="last-year">Last year</SelectItem>
            <SelectItem value="custom">Custom range</SelectItem>
          </SelectContent>
        </Select>
        {role === 'admin' && (
          <Select defaultValue="all-recruiters">
            <SelectTrigger className="w-full md:w-[200px]">
              <SelectValue placeholder="Recruiter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-recruiters">All Recruiters</SelectItem>
              <SelectItem value="sarah-johnson">Sarah Johnson</SelectItem>
              <SelectItem value="mike-wilson">Mike Wilson</SelectItem>
              <SelectItem value="lisa-brown">Lisa Brown</SelectItem>
            </SelectContent>
          </Select>
        )}
        <Select defaultValue="all-departments">
          <SelectTrigger className="w-full md:w-[200px]">
            <SelectValue placeholder="Department" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all-departments">All Departments</SelectItem>
            <SelectItem value="engineering">Engineering</SelectItem>
            <SelectItem value="product">Product</SelectItem>
            <SelectItem value="design">Design</SelectItem>
            <SelectItem value="marketing">Marketing</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">247</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+12%</span> from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Jobs</CardTitle>
            <Briefcase className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-blue-600">+2</span> new postings
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Time to Hire</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">14 days</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">-2 days</span> from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">18.5%</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+3.2%</span> from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Reports Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="hiring-funnel">Hiring Funnel</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6 mt-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Applications Over Time
                </CardTitle>
                <CardDescription>Daily application volume for the last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px] flex items-center justify-center bg-muted/10 rounded-lg">
                  <LineChart className="h-16 w-16 text-muted-foreground" />
                  <span className="ml-2 text-muted-foreground">
                    Chart visualization would go here
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Application Status Distribution
                </CardTitle>
                <CardDescription>Current status breakdown of all applications</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[200px] flex items-center justify-center bg-muted/10 rounded-lg">
                  <PieChart className="h-16 w-16 text-muted-foreground" />
                  <span className="ml-2 text-muted-foreground">
                    Chart visualization would go here
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart className="h-5 w-5" />
                Department Performance
              </CardTitle>
              <CardDescription>Hiring metrics by department</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="font-medium">Engineering</span>
                  <div className="flex items-center gap-4">
                    <Badge variant="outline">45 applications</Badge>
                    <Badge variant="outline">8 hires</Badge>
                    <Badge className="bg-green-100 text-green-800">17.8% rate</Badge>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Product</span>
                  <div className="flex items-center gap-4">
                    <Badge variant="outline">23 applications</Badge>
                    <Badge variant="outline">4 hires</Badge>
                    <Badge className="bg-blue-100 text-blue-800">17.4% rate</Badge>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Design</span>
                  <div className="flex items-center gap-4">
                    <Badge variant="outline">18 applications</Badge>
                    <Badge variant="outline">3 hires</Badge>
                    <Badge className="bg-yellow-100 text-yellow-800">16.7% rate</Badge>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="font-medium">Marketing</span>
                  <div className="flex items-center gap-4">
                    <Badge variant="outline">12 applications</Badge>
                    <Badge variant="outline">2 hires</Badge>
                    <Badge className="bg-purple-100 text-purple-800">16.7% rate</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="hiring-funnel" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Hiring Funnel Analysis</CardTitle>
              <CardDescription>Track candidate progress through hiring stages</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-950 rounded-lg">
                  <div>
                    <h3 className="font-semibold">Total Applications</h3>
                    <p className="text-2xl font-bold text-blue-600">247</p>
                  </div>
                  <div className="text-blue-600">100%</div>
                </div>

                <div className="flex items-center justify-between p-4 bg-yellow-50 dark:bg-yellow-950 rounded-lg">
                  <div>
                    <h3 className="font-semibold">Initial Screening</h3>
                    <p className="text-2xl font-bold text-yellow-600">156</p>
                  </div>
                  <div className="text-yellow-600">63%</div>
                </div>

                <div className="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-950 rounded-lg">
                  <div>
                    <h3 className="font-semibold">Technical Interview</h3>
                    <p className="text-2xl font-bold text-purple-600">89</p>
                  </div>
                  <div className="text-purple-600">36%</div>
                </div>

                <div className="flex items-center justify-between p-4 bg-orange-50 dark:bg-orange-950 rounded-lg">
                  <div>
                    <h3 className="font-semibold">Final Interview</h3>
                    <p className="text-2xl font-bold text-orange-600">52</p>
                  </div>
                  <div className="text-orange-600">21%</div>
                </div>

                <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-950 rounded-lg">
                  <div>
                    <h3 className="font-semibold">Hired</h3>
                    <p className="text-2xl font-bold text-green-600">17</p>
                  </div>
                  <div className="text-green-600">6.9%</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6 mt-6">
          {role === 'admin' && (
            <Card>
              <CardHeader>
                <CardTitle>Recruiter Performance</CardTitle>
                <CardDescription>Individual recruiter metrics and performance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-semibold">Sarah Johnson</h3>
                      <p className="text-sm text-muted-foreground">Senior Recruiter</p>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold">67</p>
                        <p className="text-xs text-muted-foreground">Applications</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold">12</p>
                        <p className="text-xs text-muted-foreground">Hires</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold">17.9%</p>
                        <p className="text-xs text-muted-foreground">Success Rate</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-semibold">Mike Wilson</h3>
                      <p className="text-sm text-muted-foreground">Recruiter</p>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold">45</p>
                        <p className="text-xs text-muted-foreground">Applications</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold">8</p>
                        <p className="text-xs text-muted-foreground">Hires</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold">17.8%</p>
                        <p className="text-xs text-muted-foreground">Success Rate</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-semibold">Lisa Brown</h3>
                      <p className="text-sm text-muted-foreground">Junior Recruiter</p>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold">34</p>
                        <p className="text-xs text-muted-foreground">Applications</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold">5</p>
                        <p className="text-xs text-muted-foreground">Hires</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold">14.7%</p>
                        <p className="text-xs text-muted-foreground">Success Rate</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle>Job Performance</CardTitle>
              <CardDescription>Performance metrics for active job postings</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold">Senior Frontend Developer</h3>
                    <p className="text-sm text-muted-foreground">Engineering</p>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold">45</p>
                      <p className="text-xs text-muted-foreground">Applications</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">12</p>
                      <p className="text-xs text-muted-foreground">Days Active</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">3.8</p>
                      <p className="text-xs text-muted-foreground">Apps/Day</p>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h3 className="font-semibold">Product Manager</h3>
                    <p className="text-sm text-muted-foreground">Product</p>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold">23</p>
                      <p className="text-xs text-muted-foreground">Applications</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">8</p>
                      <p className="text-xs text-muted-foreground">Days Active</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">2.9</p>
                      <p className="text-xs text-muted-foreground">Apps/Day</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6 mt-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Source Analytics</CardTitle>
                <CardDescription>Where your best candidates come from</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>LinkedIn</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">42%</Badge>
                      <div className="w-20 bg-muted rounded-full h-2">
                        <div className="w-[42%] bg-blue-500 h-2 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Company Website</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">28%</Badge>
                      <div className="w-20 bg-muted rounded-full h-2">
                        <div className="w-[28%] bg-green-500 h-2 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Job Boards</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">18%</Badge>
                      <div className="w-20 bg-muted rounded-full h-2">
                        <div className="w-[18%] bg-yellow-500 h-2 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Referrals</span>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">12%</Badge>
                      <div className="w-20 bg-muted rounded-full h-2">
                        <div className="w-[12%] bg-purple-500 h-2 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Time to Hire Breakdown</CardTitle>
                <CardDescription>Average time spent in each hiring stage</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Application Review</span>
                    <Badge variant="outline">2.1 days</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Initial Screening</span>
                    <Badge variant="outline">3.4 days</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Technical Interview</span>
                    <Badge variant="outline">4.2 days</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Final Interview</span>
                    <Badge variant="outline">2.8 days</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Decision & Offer</span>
                    <Badge variant="outline">1.5 days</Badge>
                  </div>
                  <div className="border-t pt-4">
                    <div className="flex items-center justify-between font-semibold">
                      <span>Total Average</span>
                      <Badge>14.0 days</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );

  return <>{renderReportsView()}</>;
}
