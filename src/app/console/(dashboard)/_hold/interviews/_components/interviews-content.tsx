'use client';

import {
  Calendar,
  CheckCircle,
  Clock,
  Edit,
  Filter,
  MapPin,
  MessageSquare,
  Phone,
  Plus,
  Search,
  User,
  Video,
  XCircle,
} from 'lucide-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { useRole } from '../../../../../../contexts/role-context';

// Types
interface AdminRecruiterInterview {
  id: number;
  candidateName: string;
  candidateEmail: string;
  candidateAvatar: string;
  jobTitle: string;
  interviewType: string;
  date: string;
  time: string;
  duration: string;
  interviewer: string;
  interviewerRole: string;
  status: string;
  mode: string;
  meetingLink?: string;
  location?: string;
  notes?: string;
}

interface CandidateInterview {
  id: number;
  jobTitle: string;
  company: string;
  interviewType: string;
  date: string;
  time: string;
  duration: string;
  interviewer: string;
  interviewerRole: string;
  status: string;
  mode: string;
  meetingLink?: string;
  applicationId: string;
  preparation?: string;
  feedback?: string;
}

// Mock data for different roles
const mockInterviews = {
  admin: [
    {
      id: 1,
      candidateName: 'John Doe',
      candidateEmail: '<EMAIL>',
      candidateAvatar: '',
      jobTitle: 'Senior Frontend Developer',
      interviewType: 'Technical',
      date: '2024-01-25',
      time: '10:00 AM',
      duration: '60 min',
      interviewer: 'Sarah Johnson',
      interviewerRole: 'Tech Lead',
      status: 'Scheduled',
      mode: 'Video Call',
      meetingLink: 'https://zoom.us/j/*********',
      notes: 'Focus on React and TypeScript experience',
    },
    {
      id: 2,
      candidateName: 'Jane Smith',
      candidateEmail: '<EMAIL>',
      candidateAvatar: '',
      jobTitle: 'Product Manager',
      interviewType: 'Final',
      date: '2024-01-24',
      time: '2:00 PM',
      duration: '45 min',
      interviewer: 'Mike Wilson',
      interviewerRole: 'VP Product',
      status: 'Completed',
      mode: 'In-person',
      location: 'Conference Room A',
      notes: 'Excellent candidate, strong product sense',
    },
    {
      id: 3,
      candidateName: 'Alex Johnson',
      candidateEmail: '<EMAIL>',
      candidateAvatar: '',
      jobTitle: 'UX Designer',
      interviewType: 'Portfolio Review',
      date: '2024-01-26',
      time: '11:30 AM',
      duration: '30 min',
      interviewer: 'Lisa Brown',
      interviewerRole: 'Senior Designer',
      status: 'Cancelled',
      mode: 'Video Call',
      notes: 'Candidate requested reschedule',
    },
  ],
  recruiter: [
    {
      id: 1,
      candidateName: 'John Doe',
      candidateEmail: '<EMAIL>',
      candidateAvatar: '',
      jobTitle: 'Senior Frontend Developer',
      interviewType: 'Technical',
      date: '2024-01-25',
      time: '10:00 AM',
      duration: '60 min',
      interviewer: 'Sarah Johnson',
      interviewerRole: 'Tech Lead',
      status: 'Scheduled',
      mode: 'Video Call',
      meetingLink: 'https://zoom.us/j/*********',
      notes: 'Focus on React and TypeScript experience',
    },
    {
      id: 4,
      candidateName: 'Emma Davis',
      candidateEmail: '<EMAIL>',
      candidateAvatar: '',
      jobTitle: 'Marketing Specialist',
      interviewType: 'HR Screening',
      date: '2024-01-23',
      time: '3:00 PM',
      duration: '30 min',
      interviewer: 'You',
      interviewerRole: 'Recruiter',
      status: 'Completed',
      mode: 'Phone Call',
      notes: 'Great cultural fit, moving to next round',
    },
  ],
  candidate: [
    {
      id: 1,
      jobTitle: 'Senior Frontend Developer',
      company: 'TechCorp Inc.',
      interviewType: 'Technical Interview',
      date: '2024-01-25',
      time: '10:00 AM',
      duration: '60 min',
      interviewer: 'Sarah Johnson',
      interviewerRole: 'Tech Lead',
      status: 'Scheduled',
      mode: 'Video Call',
      meetingLink: 'https://zoom.us/j/*********',
      applicationId: 'APP-2024-001',
      preparation: 'Review React, TypeScript, and system design concepts',
    },
    {
      id: 2,
      jobTitle: 'Full Stack Engineer',
      company: 'StartupXYZ',
      interviewType: 'Final Interview',
      date: '2024-01-24',
      time: '2:00 PM',
      duration: '45 min',
      interviewer: 'Michael Chen',
      interviewerRole: 'CTO',
      status: 'Completed',
      mode: 'Video Call',
      applicationId: 'APP-2024-002',
      feedback: 'Interview went well, waiting for decision',
    },
    {
      id: 3,
      jobTitle: 'React Developer',
      company: 'Digital Agency',
      interviewType: 'HR Screening',
      date: '2024-01-22',
      time: '11:00 AM',
      duration: '30 min',
      interviewer: 'Jessica Liu',
      interviewerRole: 'HR Manager',
      status: 'Completed',
      mode: 'Phone Call',
      applicationId: 'APP-2024-003',
      feedback: 'Moved to technical round',
    },
  ],
};

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'scheduled':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'completed':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case 'cancelled':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    case 'rescheduled':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
};

const getStatusIcon = (status: string) => {
  switch (status.toLowerCase()) {
    case 'completed':
      return <CheckCircle className="h-4 w-4" />;
    case 'cancelled':
      return <XCircle className="h-4 w-4" />;
    case 'scheduled':
      return <Clock className="h-4 w-4" />;
    default:
      return <Calendar className="h-4 w-4" />;
  }
};

const getModeIcon = (mode: string) => {
  switch (mode.toLowerCase()) {
    case 'video call':
      return <Video className="h-4 w-4" />;
    case 'phone call':
      return <Phone className="h-4 w-4" />;
    case 'in-person':
      return <MapPin className="h-4 w-4" />;
    default:
      return <Calendar className="h-4 w-4" />;
  }
};

export function InterviewsContent() {
  const { role } = useRole();
  const adminRecruiterInterviews =
    role === 'admin' || role === 'recruiter'
      ? (mockInterviews[role] as AdminRecruiterInterview[])
      : [];
  const candidateInterviews =
    role === 'candidate' ? (mockInterviews[role] as CandidateInterview[]) : [];

  const renderAdminRecruiterView = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Interviews</h1>
          <p className="text-muted-foreground">Schedule and manage candidate interviews</p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Schedule Interview
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input placeholder="Search interviews..." className="pl-10" />
        </div>
        <Select defaultValue="all">
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="scheduled">Scheduled</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="cancelled">Cancelled</SelectItem>
          </SelectContent>
        </Select>
        <Select defaultValue="all">
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="hr-screening">HR Screening</SelectItem>
            <SelectItem value="technical">Technical</SelectItem>
            <SelectItem value="final">Final</SelectItem>
            <SelectItem value="portfolio">Portfolio Review</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Interviews</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{adminRecruiterInterviews.length}</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {
                adminRecruiterInterviews.filter((interview) => interview.status === 'Scheduled')
                  .length
              }
            </div>
            <p className="text-xs text-muted-foreground">Upcoming</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {
                adminRecruiterInterviews.filter((interview) => interview.status === 'Completed')
                  .length
              }
            </div>
            <p className="text-xs text-muted-foreground">This week</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">85%</div>
            <p className="text-xs text-muted-foreground">Interview to hire</p>
          </CardContent>
        </Card>
      </div>

      {/* Interviews List */}
      <div className="space-y-4">
        {adminRecruiterInterviews.map((interview) => (
          <Card key={interview.id}>
            <CardContent className="p-6">
              <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                <div className="flex items-start space-x-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={interview.candidateAvatar} />
                    <AvatarFallback>
                      {interview.candidateName
                        .split(' ')
                        .map((n: string) => n[0])
                        .join('')}
                    </AvatarFallback>
                  </Avatar>
                  <div className="space-y-2">
                    <div>
                      <h3 className="text-lg font-semibold">{interview.candidateName}</h3>
                      <p className="text-sm text-muted-foreground">{interview.candidateEmail}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(interview.status)}>
                        {getStatusIcon(interview.status)}
                        <span className="ml-1">{interview.status}</span>
                      </Badge>
                      <Badge variant="outline">{interview.interviewType}</Badge>
                    </div>
                    <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {interview.date} at {interview.time}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {interview.duration}
                      </div>
                      <div className="flex items-center gap-1">
                        {getModeIcon(interview.mode)}
                        {interview.mode}
                      </div>
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {interview.interviewer} ({interview.interviewerRole})
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{interview.jobTitle}</Badge>
                    </div>
                    {interview.notes && (
                      <p className="text-sm text-muted-foreground">{interview.notes}</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {interview.status === 'Scheduled' && (
                    <>
                      {interview.meetingLink && (
                        <Button size="sm">
                          <Video className="mr-2 h-4 w-4" />
                          Join
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </Button>
                    </>
                  )}
                  {interview.status === 'Completed' && (
                    <Button variant="outline" size="sm">
                      View Feedback
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Message
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderCandidateView = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">My Interviews</h1>
          <p className="text-muted-foreground">View and manage your upcoming interviews</p>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input placeholder="Search interviews..." className="pl-10" />
        </div>
        <Select defaultValue="all">
          <SelectTrigger className="w-full md:w-[180px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="scheduled">Upcoming</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Interviews Tabs */}
      <Tabs defaultValue="upcoming" className="w-full">
        <TabsList>
          <TabsTrigger value="upcoming">
            Upcoming ({candidateInterviews.filter((i) => i.status === 'Scheduled').length})
          </TabsTrigger>
          <TabsTrigger value="completed">
            Completed ({candidateInterviews.filter((i) => i.status === 'Completed').length})
          </TabsTrigger>
          <TabsTrigger value="all">All Interviews ({candidateInterviews.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="upcoming" className="space-y-4 mt-6">
          {candidateInterviews
            .filter((interview) => interview.status === 'Scheduled')
            .map((interview) => (
              <Card
                key={interview.id}
                className="hover:shadow-md transition-shadow border-l-4 border-l-blue-500"
              >
                <CardContent className="p-6">
                  <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-lg font-semibold">{interview.jobTitle}</h3>
                          <p className="text-muted-foreground">{interview.company}</p>
                        </div>
                        <Badge className={getStatusColor(interview.status)}>
                          {getStatusIcon(interview.status)}
                          <span className="ml-1">{interview.status}</span>
                        </Badge>
                      </div>

                      <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {interview.date} at {interview.time}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {interview.duration}
                        </div>
                        <div className="flex items-center gap-1">
                          {getModeIcon(interview.mode)}
                          {interview.mode}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{interview.interviewType}</Badge>
                        <Badge variant="outline">with {interview.interviewer}</Badge>
                      </div>

                      {interview.preparation && (
                        <div className="bg-blue-50 dark:bg-blue-950 p-3 rounded-lg">
                          <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                            Preparation Notes:
                          </p>
                          <p className="text-sm text-blue-800 dark:text-blue-200">
                            {interview.preparation}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col gap-2 md:ml-4">
                      {interview.meetingLink && (
                        <Button>
                          <Video className="mr-2 h-4 w-4" />
                          Join Interview
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        <Calendar className="mr-2 h-4 w-4" />
                        Add to Calendar
                      </Button>
                      <Button variant="outline" size="sm">
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Contact Interviewer
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          {candidateInterviews.filter((interview) => interview.status === 'Scheduled').length ===
            0 && (
            <Card>
              <CardContent className="p-6 text-center">
                <Calendar className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No upcoming interviews</h3>
                <p className="text-muted-foreground">Your scheduled interviews will appear here.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="completed" className="space-y-4 mt-6">
          {candidateInterviews
            .filter((interview) => interview.status === 'Completed')
            .map((interview) => (
              <Card key={interview.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="text-lg font-semibold">{interview.jobTitle}</h3>
                          <p className="text-muted-foreground">{interview.company}</p>
                        </div>
                        <Badge className={getStatusColor(interview.status)}>
                          {getStatusIcon(interview.status)}
                          <span className="ml-1">{interview.status}</span>
                        </Badge>
                      </div>

                      <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {interview.date} at {interview.time}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {interview.duration}
                        </div>
                        <div className="flex items-center gap-1">
                          {getModeIcon(interview.mode)}
                          {interview.mode}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{interview.interviewType}</Badge>
                        <Badge variant="outline">with {interview.interviewer}</Badge>
                        <Badge variant="outline">App ID: {interview.applicationId}</Badge>
                      </div>

                      {interview.feedback && (
                        <div className="bg-green-50 dark:bg-green-950 p-3 rounded-lg">
                          <p className="text-sm font-medium text-green-900 dark:text-green-100">
                            Feedback:
                          </p>
                          <p className="text-sm text-green-800 dark:text-green-200">
                            {interview.feedback}
                          </p>
                        </div>
                      )}
                    </div>

                    <div className="flex flex-col gap-2 md:ml-4">
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
        </TabsContent>

        <TabsContent value="all" className="space-y-4 mt-6">
          {candidateInterviews.map((interview) => (
            <Card key={interview.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                  <div className="space-y-3 flex-1">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-lg font-semibold">{interview.jobTitle}</h3>
                        <p className="text-muted-foreground">{interview.company}</p>
                      </div>
                      <Badge className={getStatusColor(interview.status)}>
                        {getStatusIcon(interview.status)}
                        <span className="ml-1">{interview.status}</span>
                      </Badge>
                    </div>

                    <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {interview.date} at {interview.time}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {interview.duration}
                      </div>
                      <div className="flex items-center gap-1">
                        {getModeIcon(interview.mode)}
                        {interview.mode}
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{interview.interviewType}</Badge>
                      <Badge variant="outline">with {interview.interviewer}</Badge>
                      <Badge variant="outline">App ID: {interview.applicationId}</Badge>
                    </div>
                  </div>

                  <div className="flex flex-col gap-2 md:ml-4">
                    {interview.status === 'Scheduled' && interview.meetingLink && (
                      <Button>
                        <Video className="mr-2 h-4 w-4" />
                        Join Interview
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );

  return <>{role === 'candidate' ? renderCandidateView() : renderAdminRecruiterView()}</>;
}
