'use client';

import {
  Bookmark,
  BookmarkX,
  Calendar,
  DollarSign,
  ExternalLink,
  Eye,
  Filter,
  Heart,
  MapPin,
  Search,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { useRole } from '../../../../../../contexts/role-context';

// Mock data for saved jobs
const mockSavedJobs = [
  {
    id: 1,
    jobTitle: 'Senior Backend Engineer',
    company: 'CloudTech Solutions',
    location: 'Seattle, WA',
    salary: '$130k - $160k',
    type: 'Full-time',
    savedDate: '2024-01-21',
    postedDate: '2024-01-19',
    match: 92,
    description: 'Join our team to build scalable cloud infrastructure and microservices...',
    tags: ['Python', 'AWS', 'Kubernetes', 'Microservices'],
    urgency: 'high',
    deadline: '2024-01-30',
    applied: false,
  },
  {
    id: 2,
    jobTitle: 'Lead Product Designer',
    company: 'InnovateCo',
    location: 'Remote',
    salary: '$120k - $150k',
    type: 'Full-time',
    savedDate: '2024-01-20',
    postedDate: '2024-01-18',
    match: 88,
    description: 'Lead design initiatives for our flagship product used by millions...',
    tags: ['Figma', 'Design Systems', 'User Research', 'Prototyping'],
    urgency: 'medium',
    deadline: '2024-02-05',
    applied: false,
  },
  {
    id: 3,
    jobTitle: 'DevOps Engineer',
    company: 'TechFlow Inc.',
    location: 'Austin, TX',
    salary: '$110k - $140k',
    type: 'Full-time',
    savedDate: '2024-01-18',
    postedDate: '2024-01-15',
    match: 85,
    description: 'Help us scale our infrastructure and improve deployment processes...',
    tags: ['Docker', 'CI/CD', 'Jenkins', 'AWS'],
    urgency: 'low',
    deadline: '2024-02-10',
    applied: true,
  },
  {
    id: 4,
    jobTitle: 'Frontend Architect',
    company: 'WebCraft Studio',
    location: 'San Francisco, CA',
    salary: '$140k - $170k',
    type: 'Full-time',
    savedDate: '2024-01-17',
    postedDate: '2024-01-16',
    match: 95,
    description: 'Shape the future of our frontend architecture and mentor developers...',
    tags: ['React', 'TypeScript', 'Architecture', 'Leadership'],
    urgency: 'high',
    deadline: '2024-01-28',
    applied: false,
  },
  {
    id: 5,
    jobTitle: 'Data Scientist',
    company: 'AnalyticsPro',
    location: 'Boston, MA',
    salary: '$115k - $145k',
    type: 'Full-time',
    savedDate: '2024-01-16',
    postedDate: '2024-01-14',
    match: 78,
    description: 'Use machine learning to drive business insights and decisions...',
    tags: ['Python', 'Machine Learning', 'SQL', 'TensorFlow'],
    urgency: 'medium',
    deadline: '2024-02-15',
    applied: false,
  },
  {
    id: 6,
    jobTitle: 'Mobile App Developer',
    company: 'AppGenius',
    location: 'Los Angeles, CA',
    salary: '$100k - $130k',
    type: 'Contract',
    savedDate: '2024-01-15',
    postedDate: '2024-01-12',
    match: 82,
    description: 'Develop innovative mobile applications for iOS and Android...',
    tags: ['React Native', 'iOS', 'Android', 'Mobile'],
    urgency: 'low',
    deadline: '2024-02-20',
    applied: false,
  },
];

const getMatchColor = (match: number) => {
  if (match >= 90) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
  if (match >= 80) return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
  if (match >= 70) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
  return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
};

const getUrgencyColor = (urgency: string) => {
  switch (urgency) {
    case 'high':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    default:
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
  }
};

const getDaysUntilDeadline = (deadline: string) => {
  const today = new Date();
  const deadlineDate = new Date(deadline);
  const diffTime = deadlineDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
};

export function SavedJobsContent() {
  const { role } = useRole();

  // Redirect non-candidates away from saved jobs
  if (role !== 'candidate') {
    return (
      <>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <div className="text-center">
            <h2 className="text-2xl font-bold">Access Restricted</h2>
            <p className="text-muted-foreground">Saved jobs are only available to candidates.</p>
          </div>
        </div>
      </>
    );
  }

  const notAppliedJobs = mockSavedJobs.filter((job) => !job.applied);
  const appliedJobs = mockSavedJobs.filter((job) => job.applied);
  const urgentJobs = mockSavedJobs.filter((job) => getDaysUntilDeadline(job.deadline) <= 5);

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Saved Jobs</h1>
            <p className="text-muted-foreground">Keep track of interesting opportunities</p>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Saved</CardTitle>
              <Bookmark className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockSavedJobs.length}</div>
              <p className="text-xs text-muted-foreground">+3 this week</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Not Applied</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{notAppliedJobs.length}</div>
              <p className="text-xs text-muted-foreground">Ready to apply</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Urgent Deadlines</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{urgentJobs.length}</div>
              <p className="text-xs text-muted-foreground">Within 5 days</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Match Score</CardTitle>
              <Heart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(
                  mockSavedJobs.reduce((sum, job) => sum + job.match, 0) / mockSavedJobs.length
                )}
                %
              </div>
              <p className="text-xs text-muted-foreground">Compatibility</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input placeholder="Search saved jobs..." className="pl-10" />
          </div>
          <Select defaultValue="all">
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="Match Score" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Matches</SelectItem>
              <SelectItem value="high">90%+ Match</SelectItem>
              <SelectItem value="medium">80-89% Match</SelectItem>
              <SelectItem value="low">Below 80%</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="all">
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="Location" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Locations</SelectItem>
              <SelectItem value="remote">Remote</SelectItem>
              <SelectItem value="onsite">On-site</SelectItem>
              <SelectItem value="hybrid">Hybrid</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>

        {/* Saved Jobs Tabs */}
        <Tabs defaultValue="all" className="w-full">
          <TabsList>
            <TabsTrigger value="all">All Saved ({mockSavedJobs.length})</TabsTrigger>
            <TabsTrigger value="not-applied">Not Applied ({notAppliedJobs.length})</TabsTrigger>
            <TabsTrigger value="urgent">Urgent ({urgentJobs.length})</TabsTrigger>
            <TabsTrigger value="applied">Already Applied ({appliedJobs.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4 mt-6">
            {mockSavedJobs.map((job) => {
              const daysLeft = getDaysUntilDeadline(job.deadline);
              return (
                <Card
                  key={job.id}
                  className={`hover:shadow-md transition-shadow ${job.applied ? 'opacity-75' : ''} ${daysLeft <= 3 ? 'border-l-4 border-l-red-500' : ''}`}
                >
                  <CardContent className="p-6">
                    <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                      <div className="space-y-3 flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="text-lg font-semibold">{job.jobTitle}</h3>
                              {job.applied && (
                                <Badge className="bg-blue-100 text-blue-800">Applied</Badge>
                              )}
                            </div>
                            <p className="text-muted-foreground">{job.company}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className={getMatchColor(job.match)}>{job.match}% match</Badge>
                            {daysLeft <= 5 && (
                              <Badge className={getUrgencyColor('high')}>
                                {daysLeft} days left
                              </Badge>
                            )}
                          </div>
                        </div>

                        <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            {job.location}
                          </div>
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4" />
                            {job.salary}
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            Posted {job.postedDate}
                          </div>
                          <div className="flex items-center gap-1">
                            <Bookmark className="h-4 w-4" />
                            Saved {job.savedDate}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{job.type}</Badge>
                          {job.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline">
                              {tag}
                            </Badge>
                          ))}
                          {job.tags.length > 3 && (
                            <Badge variant="outline">+{job.tags.length - 3} more</Badge>
                          )}
                        </div>

                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {job.description}
                        </p>

                        {daysLeft <= 5 && (
                          <div className="bg-red-50 dark:bg-red-950 p-3 rounded-lg">
                            <p className="text-sm font-medium text-red-900 dark:text-red-100">
                              ⚠️ Application deadline: {job.deadline} ({daysLeft} days remaining)
                            </p>
                          </div>
                        )}
                      </div>

                      <div className="flex flex-col gap-2 md:ml-4">
                        {!job.applied ? (
                          <>
                            <Button>Apply Now</Button>
                            <Button variant="outline" size="sm">
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </Button>
                          </>
                        ) : (
                          <Button variant="outline">
                            <Eye className="mr-2 h-4 w-4" />
                            View Application
                          </Button>
                        )}
                        <Button variant="outline" size="sm">
                          <ExternalLink className="mr-2 h-4 w-4" />
                          Company Page
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:bg-red-50 hover:text-red-700"
                        >
                          <BookmarkX className="mr-2 h-4 w-4" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>

          <TabsContent value="not-applied" className="space-y-4 mt-6">
            {notAppliedJobs.map((job) => {
              const daysLeft = getDaysUntilDeadline(job.deadline);
              return (
                <Card
                  key={job.id}
                  className={`hover:shadow-md transition-shadow ${daysLeft <= 3 ? 'border-l-4 border-l-red-500' : ''}`}
                >
                  <CardContent className="p-6">
                    <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                      <div className="space-y-3 flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="text-lg font-semibold">{job.jobTitle}</h3>
                            <p className="text-muted-foreground">{job.company}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className={getMatchColor(job.match)}>{job.match}% match</Badge>
                            {daysLeft <= 5 && (
                              <Badge className={getUrgencyColor('high')}>
                                {daysLeft} days left
                              </Badge>
                            )}
                          </div>
                        </div>

                        <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            {job.location}
                          </div>
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4" />
                            {job.salary}
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            Deadline: {job.deadline}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{job.type}</Badge>
                          {job.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline">
                              {tag}
                            </Badge>
                          ))}
                        </div>

                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {job.description}
                        </p>

                        {daysLeft <= 5 && (
                          <div className="bg-red-50 dark:bg-red-950 p-3 rounded-lg">
                            <p className="text-sm font-medium text-red-900 dark:text-red-100">
                              ⚠️ Application deadline: {job.deadline} ({daysLeft} days remaining)
                            </p>
                          </div>
                        )}
                      </div>

                      <div className="flex flex-col gap-2 md:ml-4">
                        <Button className={daysLeft <= 3 ? 'bg-red-600 hover:bg-red-700' : ''}>
                          {daysLeft <= 3 ? 'Apply Urgently!' : 'Apply Now'}
                        </Button>
                        <Button variant="outline" size="sm">
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:bg-red-50"
                        >
                          <BookmarkX className="mr-2 h-4 w-4" />
                          Remove
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}

            {notAppliedJobs.length === 0 && (
              <Card>
                <CardContent className="p-6 text-center">
                  <Bookmark className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">All caught up!</h3>
                  <p className="text-muted-foreground">
                    You&apos;ve applied to all your saved jobs.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="urgent" className="space-y-4 mt-6">
            {urgentJobs.map((job) => {
              const daysLeft = getDaysUntilDeadline(job.deadline);
              return (
                <Card
                  key={job.id}
                  className="hover:shadow-md transition-shadow border-l-4 border-l-red-500"
                >
                  <CardContent className="p-6">
                    <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                      <div className="space-y-3 flex-1">
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="flex items-center gap-2">
                              <h3 className="text-lg font-semibold">{job.jobTitle}</h3>
                              {job.applied && (
                                <Badge className="bg-blue-100 text-blue-800">Applied</Badge>
                              )}
                            </div>
                            <p className="text-muted-foreground">{job.company}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className={getMatchColor(job.match)}>{job.match}% match</Badge>
                            <Badge className={getUrgencyColor('high')}>{daysLeft} days left</Badge>
                          </div>
                        </div>

                        <div className="bg-red-50 dark:bg-red-950 p-3 rounded-lg">
                          <p className="text-sm font-medium text-red-900 dark:text-red-100">
                            🚨 Urgent: Application deadline {job.deadline} ({daysLeft} days
                            remaining)
                          </p>
                        </div>

                        <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            {job.location}
                          </div>
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4" />
                            {job.salary}
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{job.type}</Badge>
                          {job.tags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="outline">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="flex flex-col gap-2 md:ml-4">
                        {!job.applied ? (
                          <Button className="bg-red-600 hover:bg-red-700">Apply Urgently!</Button>
                        ) : (
                          <Button variant="outline">
                            <Eye className="mr-2 h-4 w-4" />
                            View Application
                          </Button>
                        )}
                        <Button variant="outline" size="sm">
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}

            {urgentJobs.length === 0 && (
              <Card>
                <CardContent className="p-6 text-center">
                  <Calendar className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No urgent deadlines</h3>
                  <p className="text-muted-foreground">
                    All your saved jobs have comfortable deadlines.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="applied" className="space-y-4 mt-6">
            {appliedJobs.map((job) => (
              <Card key={job.id} className="hover:shadow-md transition-shadow opacity-75">
                <CardContent className="p-6">
                  <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="text-lg font-semibold">{job.jobTitle}</h3>
                            <Badge className="bg-blue-100 text-blue-800">Applied</Badge>
                          </div>
                          <p className="text-muted-foreground">{job.company}</p>
                        </div>
                        <Badge className={getMatchColor(job.match)}>{job.match}% match</Badge>
                      </div>

                      <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {job.location}
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          {job.salary}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Saved {job.savedDate}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{job.type}</Badge>
                        {job.tags.slice(0, 3).map((tag, index) => (
                          <Badge key={index} variant="outline">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 md:ml-4">
                      <Button variant="outline">
                        <Eye className="mr-2 h-4 w-4" />
                        View Application
                      </Button>
                      <Button variant="outline" size="sm" className="text-red-600 hover:bg-red-50">
                        <BookmarkX className="mr-2 h-4 w-4" />
                        Remove
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
