'use client';

import {
  IconBriefcase,
  IconCalendar,
  IconEye,
  IconFilter,
  IconMapPin,
  IconSearch,
  IconUsers,
} from '@tabler/icons-react';
import { DollarSignIcon } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { useRole } from '../../../../../../contexts/role-context';

// Mock data for jobs
const mockJobs = [
  {
    id: 1,
    title: 'Senior Frontend Developer',
    company: 'TechCorp Inc.',
    location: 'San Francisco, CA',
    type: 'Full-time',
    salary: '$120k - $150k',
    postedDate: '2024-01-20',
    status: 'Active',
    applicants: 45,
    views: 234,
    description: 'We are looking for a senior frontend developer with React expertise...',
    requirements: ['5+ years React experience', 'TypeScript', 'Next.js'],
  },
  {
    id: 2,
    title: 'Full Stack Engineer',
    company: 'StartupXYZ',
    location: 'Remote',
    type: 'Full-time',
    salary: '$100k - $130k',
    postedDate: '2024-01-18',
    status: 'Active',
    applicants: 67,
    views: 189,
    description: 'Join our growing team as a full stack engineer...',
    requirements: ['Node.js', 'React', 'PostgreSQL'],
  },
  {
    id: 3,
    title: 'React Developer',
    company: 'Digital Agency',
    location: 'New York, NY',
    type: 'Contract',
    salary: '$80k - $100k',
    postedDate: '2024-01-15',
    status: 'Closed',
    applicants: 23,
    views: 156,
    description: 'Contract position for an experienced React developer...',
    requirements: ['3+ years React', 'CSS/SCSS', 'Git'],
  },
];

export function JobsContent() {
  const { role } = useRole();

  const _getPageTitle = () => {
    switch (role) {
      case 'admin':
        return 'All Jobs';
      case 'recruiter':
        return 'My Jobs';
      case 'candidate':
        return 'Available Jobs';
      default:
        return 'Jobs';
    }
  };

  const _getPageDescription = () => {
    switch (role) {
      case 'admin':
        return 'Manage all job postings across the platform';
      case 'recruiter':
        return 'Manage your job postings and track applications';
      case 'candidate':
        return 'Discover and apply to available job opportunities';
      default:
        return 'Job listings';
    }
  };

  const canViewApplications = role === 'admin' || role === 'recruiter';

  return (
    <>
      <div className="space-y-6">
        {/* Filters */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="flex flex-1 gap-2">
            <div className="relative flex-1 max-w-sm">
              <IconSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input placeholder="Search jobs..." className="pl-9" />
            </div>
            <Select>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Location" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Locations</SelectItem>
                <SelectItem value="remote">Remote</SelectItem>
                <SelectItem value="san-francisco">San Francisco</SelectItem>
                <SelectItem value="new-york">New York</SelectItem>
              </SelectContent>
            </Select>
            <Select>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Job Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="full-time">Full-time</SelectItem>
                <SelectItem value="part-time">Part-time</SelectItem>
                <SelectItem value="contract">Contract</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button variant="outline" className="gap-2">
            <IconFilter className="h-4 w-4" />
            More Filters
          </Button>
        </div>

        {/* Jobs List */}
        <div className="space-y-4">
          {mockJobs.map((job) => (
            <Card key={job.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <CardTitle className="text-lg">{job.title}</CardTitle>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <IconBriefcase className="h-4 w-4" />
                        {job.company}
                      </div>
                      <div className="flex items-center gap-1">
                        <IconMapPin className="h-4 w-4" />
                        {job.location}
                      </div>
                      <div className="flex items-center gap-1">
                        <IconCalendar className="h-4 w-4" />
                        {job.postedDate}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={job.status === 'Active' ? 'default' : 'secondary'}>
                      {job.status}
                    </Badge>
                    <Badge variant="outline">{job.type}</Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground line-clamp-2">{job.description}</p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-sm">
                      <div className="flex items-center gap-1">
                        <DollarSignIcon className="h-4 w-4 text-green-600" />
                        <span className="font-medium">{job.salary}</span>
                      </div>
                      {canViewApplications && (
                        <>
                          <div className="flex items-center gap-1">
                            <IconUsers className="h-4 w-4 text-blue-600" />
                            <span>{job.applicants} applicants</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <IconEye className="h-4 w-4 text-gray-600" />
                            <span>{job.views} views</span>
                          </div>
                        </>
                      )}
                    </div>

                    <div className="flex gap-2">
                      {role === 'candidate' && job.status === 'Active' && (
                        <Button size="sm">Apply Now</Button>
                      )}
                      {canViewApplications && (
                        <>
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </>
  );
}
