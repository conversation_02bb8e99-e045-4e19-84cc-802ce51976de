'use client';

import {
  IconBriefcase,
  IconCalendar,
  IconCamera,
  IconEdit,
  IconMail,
  IconMapPin,
  IconPhone,
  IconStar,
  IconUser,
} from '@tabler/icons-react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

import { useRole } from '../../../../../../contexts/role-context';

export function ProfileContent() {
  const { role } = useRole();

  const getProfileData = () => {
    switch (role) {
      case 'admin':
        return {
          name: '<PERSON>',
          email: '<EMAIL>',
          title: 'Platform Administrator',
          department: 'Operations',
          joinDate: 'January 2023',
          location: 'San Francisco, CA',
          phone: '+****************',
          bio: 'Experienced platform administrator with over 8 years in HR technology. Passionate about creating efficient recruitment processes and improving user experiences.',
          stats: [
            { label: 'Organizations', value: '12', icon: IconBriefcase },
            { label: 'Users Managed', value: '234', icon: IconUser },
            { label: 'System Uptime', value: '99.9%', icon: IconStar },
          ],
        };

      case 'recruiter':
        return {
          name: 'Sarah Recruiter',
          email: '<EMAIL>',
          title: 'Senior Technical Recruiter',
          department: 'Human Resources',
          joinDate: 'March 2023',
          location: 'New York, NY',
          phone: '+****************',
          bio: 'Passionate technical recruiter with 6+ years of experience in finding top talent for software engineering roles. Specialized in full-stack developers and DevOps engineers.',
          stats: [
            { label: 'Active Jobs', value: '8', icon: IconBriefcase },
            { label: 'Candidates Hired', value: '45', icon: IconUser },
            { label: 'Success Rate', value: '78%', icon: IconStar },
          ],
        };

      case 'candidate':
        return {
          name: 'Alex Candidate',
          email: '<EMAIL>',
          title: 'Full Stack Developer',
          department: 'Engineering',
          joinDate: 'June 2024',
          location: 'Austin, TX',
          phone: '+****************',
          bio: 'Passionate full-stack developer with 4+ years of experience in React, Node.js, and cloud technologies. Love building scalable applications and learning new technologies.',
          stats: [
            { label: 'Applications', value: '12', icon: IconBriefcase },
            { label: 'Profile Views', value: '89', icon: IconUser },
            { label: 'Interview Rate', value: '65%', icon: IconStar },
          ],
        };

      default:
        return {
          name: 'User',
          email: '<EMAIL>',
          title: 'User',
          department: 'General',
          joinDate: 'Recently',
          location: 'Unknown',
          phone: '',
          bio: '',
          stats: [],
        };
    }
  };

  const profile = getProfileData();

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Header */}
      <div className="space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">Profile</h2>
        <p className="text-muted-foreground">Manage your profile information and settings</p>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        {/* Profile Overview */}
        <Card className="md:col-span-1">
          <CardHeader className="text-center">
            <div className="relative mx-auto">
              <Avatar className="h-24 w-24 mx-auto">
                <AvatarImage src="/avatars/user-avatar.jpg" alt={profile.name} />
                <AvatarFallback className="text-lg">
                  {profile.name
                    .split(' ')
                    .map((n) => n[0])
                    .join('')}
                </AvatarFallback>
              </Avatar>
              <Button
                size="sm"
                variant="outline"
                className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0"
              >
                <IconCamera className="h-4 w-4" />
              </Button>
            </div>
            <div className="space-y-1">
              <CardTitle className="text-xl">{profile.name}</CardTitle>
              <CardDescription>{profile.title}</CardDescription>
              <Badge variant="outline" className="capitalize">
                {role}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <IconMail className="h-4 w-4 text-muted-foreground" />
                <span>{profile.email}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <IconPhone className="h-4 w-4 text-muted-foreground" />
                <span>{profile.phone}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <IconMapPin className="h-4 w-4 text-muted-foreground" />
                <span>{profile.location}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <IconCalendar className="h-4 w-4 text-muted-foreground" />
                <span>Joined {profile.joinDate}</span>
              </div>
            </div>

            {/* Stats */}
            <div className="space-y-2 pt-4">
              <h4 className="font-medium">Statistics</h4>
              <div className="space-y-2">
                {profile.stats.map((stat, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-muted/50 rounded-md"
                  >
                    <div className="flex items-center gap-2">
                      <stat.icon className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{stat.label}</span>
                    </div>
                    <span className="font-medium">{stat.value}</span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Profile Details */}
        <Card className="md:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Profile Details</CardTitle>
                <CardDescription>Update your personal information</CardDescription>
              </div>
              <Button size="sm" variant="outline">
                <IconEdit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h4 className="font-medium">Basic Information</h4>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="full-name">Full Name</Label>
                  <Input id="full-name" defaultValue={profile.name} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" defaultValue={profile.email} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input id="phone" defaultValue={profile.phone} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location">Location</Label>
                  <Input id="location" defaultValue={profile.location} />
                </div>
              </div>
            </div>

            {/* Professional Information */}
            <div className="space-y-4">
              <h4 className="font-medium">Professional Information</h4>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="job-title">Job Title</Label>
                  <Input id="job-title" defaultValue={profile.title} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="department">Department</Label>
                  <Input id="department" defaultValue={profile.department} />
                </div>
              </div>
            </div>

            {/* Bio */}
            <div className="space-y-4">
              <h4 className="font-medium">Biography</h4>
              <div className="space-y-2">
                <Label htmlFor="bio">About Me</Label>
                <Textarea
                  id="bio"
                  placeholder="Tell us about yourself..."
                  defaultValue={profile.bio}
                  rows={4}
                />
              </div>
            </div>

            {/* Role-specific fields */}
            {role === 'candidate' && (
              <div className="space-y-4">
                <h4 className="font-medium">Career Preferences</h4>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="experience">Years of Experience</Label>
                    <Input id="experience" placeholder="e.g., 5 years" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="salary">Expected Salary</Label>
                    <Input id="salary" placeholder="e.g., $80,000 - $100,000" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="skills">Skills</Label>
                    <Input id="skills" placeholder="e.g., React, Node.js, Python" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="availability">Availability</Label>
                    <Input id="availability" placeholder="e.g., Available immediately" />
                  </div>
                </div>
              </div>
            )}

            {role === 'recruiter' && (
              <div className="space-y-4">
                <h4 className="font-medium">Recruiting Specialization</h4>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="specialization">Specialization</Label>
                    <Input id="specialization" placeholder="e.g., Technical Recruiting" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="industries">Industries</Label>
                    <Input id="industries" placeholder="e.g., Technology, Healthcare" />
                  </div>
                </div>
              </div>
            )}

            <div className="flex gap-2 pt-4">
              <Button>Save Changes</Button>
              <Button variant="outline">Cancel</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
