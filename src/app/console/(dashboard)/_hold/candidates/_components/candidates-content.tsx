'use client';

import {
  IconBriefcase,
  IconCalendar,
  IconEye,
  IconFilter,
  IconMail,
  IconMapPin,
  IconMessageCircle,
  IconPhone,
  IconSearch,
  IconStar,
  IconUser,
} from '@tabler/icons-react';
import { useState } from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

import { useRole } from '../../../../../../contexts/role-context';

interface Candidate {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  title: string;
  experience: string;
  skills: string[];
  rating: number;
  status: 'active' | 'interviewing' | 'hired' | 'rejected';
  appliedDate: string;
  avatar?: string;
}

export function CandidatesContent() {
  const { role } = useRole();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  // Mock data
  const [candidates] = useState<Candidate[]>([
    {
      id: '1',
      name: 'Alice Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'San Francisco, CA',
      title: 'Senior Frontend Developer',
      experience: '5+ years',
      skills: ['React', 'TypeScript', 'Node.js', 'GraphQL'],
      rating: 4.8,
      status: 'interviewing',
      appliedDate: '2024-01-15',
    },
    {
      id: '2',
      name: 'Bob Smith',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'New York, NY',
      title: 'Full Stack Developer',
      experience: '3+ years',
      skills: ['Python', 'Django', 'React', 'PostgreSQL'],
      rating: 4.5,
      status: 'active',
      appliedDate: '2024-01-12',
    },
    {
      id: '3',
      name: 'Carol Davis',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'Austin, TX',
      title: 'UX Designer',
      experience: '4+ years',
      skills: ['Figma', 'Adobe XD', 'User Research', 'Prototyping'],
      rating: 4.9,
      status: 'hired',
      appliedDate: '2024-01-10',
    },
    {
      id: '4',
      name: 'David Wilson',
      email: '<EMAIL>',
      phone: '+****************',
      location: 'Seattle, WA',
      title: 'DevOps Engineer',
      experience: '6+ years',
      skills: ['AWS', 'Docker', 'Kubernetes', 'Terraform'],
      rating: 4.6,
      status: 'active',
      appliedDate: '2024-01-08',
    },
  ]);

  const getStatusColor = (status: Candidate['status']) => {
    switch (status) {
      case 'active':
        return 'bg-blue-500/10 text-blue-700 border-blue-500/20';
      case 'interviewing':
        return 'bg-yellow-500/10 text-yellow-700 border-yellow-500/20';
      case 'hired':
        return 'bg-green-500/10 text-green-700 border-green-500/20';
      case 'rejected':
        return 'bg-red-500/10 text-red-700 border-red-500/20';
      default:
        return 'bg-gray-500/10 text-gray-700 border-gray-500/20';
    }
  };

  const filteredCandidates = candidates.filter((candidate) => {
    const matchesSearch =
      candidate.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      candidate.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      candidate.skills.some((skill) => skill.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesStatus = selectedStatus === 'all' || candidate.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  const stats = {
    total: candidates.length,
    active: candidates.filter((c) => c.status === 'active').length,
    interviewing: candidates.filter((c) => c.status === 'interviewing').length,
    hired: candidates.filter((c) => c.status === 'hired').length,
  };

  if (role === 'candidate') {
    return (
      <div className="flex flex-1 items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <IconUser className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium">Access Restricted</h3>
            <p className="text-muted-foreground">
              This page is only available to administrators and recruiters.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h2 className="text-2xl font-bold tracking-tight">Candidates</h2>
          <p className="text-muted-foreground">Manage and review candidate profiles</p>
        </div>
        <Button>
          <IconUser className="h-4 w-4 mr-2" />
          Add Candidate
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Candidates</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <IconUser className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Active</p>
                <p className="text-2xl font-bold">{stats.active}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-blue-500/10 flex items-center justify-center">
                <div className="h-3 w-3 rounded-full bg-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Interviewing</p>
                <p className="text-2xl font-bold">{stats.interviewing}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-yellow-500/10 flex items-center justify-center">
                <div className="h-3 w-3 rounded-full bg-yellow-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Hired</p>
                <p className="text-2xl font-bold">{stats.hired}</p>
              </div>
              <div className="h-8 w-8 rounded-full bg-green-500/10 flex items-center justify-center">
                <div className="h-3 w-3 rounded-full bg-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div className="relative flex-1 max-w-md">
              <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search candidates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <IconFilter className="h-4 w-4 mr-2" />
                Filter
              </Button>

              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 text-sm border rounded-md bg-background"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="interviewing">Interviewing</option>
                <option value="hired">Hired</option>
                <option value="rejected">Rejected</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Candidates List */}
      <div className="grid gap-4">
        {filteredCandidates.map((candidate) => (
          <Card key={candidate.id} className="transition-all duration-200 hover:shadow-md">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={candidate.avatar} alt={candidate.name} />
                  <AvatarFallback>
                    {candidate.name
                      .split(' ')
                      .map((n) => n[0])
                      .join('')}
                  </AvatarFallback>
                </Avatar>

                <div className="flex-1 space-y-2">
                  <div className="flex items-start justify-between">
                    <div>
                      <h3 className="font-semibold text-lg">{candidate.name}</h3>
                      <p className="text-muted-foreground">{candidate.title}</p>
                    </div>
                    <Badge className={getStatusColor(candidate.status)}>{candidate.status}</Badge>
                  </div>

                  <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-4">
                    <div className="flex items-center gap-2 text-sm">
                      <IconMail className="h-4 w-4 text-muted-foreground" />
                      <span>{candidate.email}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <IconPhone className="h-4 w-4 text-muted-foreground" />
                      <span>{candidate.phone}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <IconMapPin className="h-4 w-4 text-muted-foreground" />
                      <span>{candidate.location}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <IconBriefcase className="h-4 w-4 text-muted-foreground" />
                      <span>{candidate.experience}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <IconStar className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm font-medium">{candidate.rating}</span>
                    <span className="text-sm text-muted-foreground">rating</span>
                  </div>

                  <div className="flex flex-wrap gap-1">
                    {candidate.skills.map((skill, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center justify-between pt-2">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <IconCalendar className="h-4 w-4" />
                      <span>Applied {new Date(candidate.appliedDate).toLocaleDateString()}</span>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline">
                        <IconEye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                      <Button size="sm" variant="outline">
                        <IconMessageCircle className="h-4 w-4 mr-2" />
                        Message
                      </Button>
                      <Button size="sm">
                        <IconCalendar className="h-4 w-4 mr-2" />
                        Schedule
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredCandidates.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <IconUser className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No candidates found</h3>
            <p className="text-muted-foreground">
              {searchTerm
                ? 'Try adjusting your search criteria'
                : 'No candidates match the selected filters'}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
