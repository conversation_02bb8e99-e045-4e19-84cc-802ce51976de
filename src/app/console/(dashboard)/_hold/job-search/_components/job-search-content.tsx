'use client';

import {
  Bookmark,
  BookmarkCheck,
  Building,
  Calendar,
  Clock,
  DollarSign,
  Filter,
  MapPin,
  Search,
  Star,
  TrendingUp,
} from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { useRole } from '../../../../../../contexts/role-context';

// Mock data for job listings
const mockJobs = [
  {
    id: 1,
    title: 'Senior Frontend Developer',
    company: 'TechCorp Inc.',
    location: 'San Francisco, CA',
    type: 'Full-time',
    salary: '$120k - $150k',
    postedDate: '2024-01-20',
    matchScore: 95,
    isRemote: false,
    isHybrid: false,
    experience: 'Senior',
    description:
      'We are looking for a senior frontend developer with extensive React experience to join our growing team...',
    requirements: ['5+ years React experience', 'TypeScript', 'Next.js', 'GraphQL'],
    benefits: ['Health Insurance', 'Remote Work', '401k', 'Stock Options'],
    tags: ['React', 'TypeScript', 'Next.js', 'GraphQL'],
    isSaved: false,
    isApplied: false,
    companyLogo: '',
    companySize: '500-1000',
    industry: 'Technology',
    urgency: 'high',
  },
  {
    id: 2,
    title: 'Full Stack Engineer',
    company: 'StartupXYZ',
    location: 'Remote',
    type: 'Full-time',
    salary: '$100k - $130k',
    postedDate: '2024-01-18',
    matchScore: 88,
    isRemote: true,
    isHybrid: false,
    experience: 'Mid',
    description:
      'Join our fast-growing startup as a full stack engineer and help build the future of fintech...',
    requirements: ['3+ years full stack experience', 'Node.js', 'React', 'PostgreSQL'],
    benefits: ['Equity', 'Flexible Hours', 'Learning Budget', 'Health Insurance'],
    tags: ['Node.js', 'React', 'PostgreSQL', 'AWS'],
    isSaved: true,
    isApplied: false,
    companyLogo: '',
    companySize: '50-200',
    industry: 'FinTech',
    urgency: 'medium',
  },
  {
    id: 3,
    title: 'React Developer',
    company: 'Digital Agency',
    location: 'New York, NY',
    type: 'Contract',
    salary: '$80k - $100k',
    postedDate: '2024-01-15',
    matchScore: 76,
    isRemote: false,
    isHybrid: true,
    experience: 'Mid',
    description:
      'Contract position for an experienced React developer to work on exciting client projects...',
    requirements: ['3+ years React', 'CSS/SCSS', 'Git', 'Responsive Design'],
    benefits: ['Flexible Schedule', 'Project Variety', 'Skill Development'],
    tags: ['React', 'CSS', 'JavaScript', 'Figma'],
    isSaved: false,
    isApplied: true,
    companyLogo: '',
    companySize: '20-50',
    industry: 'Agency',
    urgency: 'low',
  },
  {
    id: 4,
    title: 'Frontend Architect',
    company: 'WebCraft Studio',
    location: 'Austin, TX',
    type: 'Full-time',
    salary: '$140k - $170k',
    postedDate: '2024-01-22',
    matchScore: 92,
    isRemote: false,
    isHybrid: true,
    experience: 'Senior',
    description:
      'Lead our frontend architecture decisions and mentor junior developers in a creative environment...',
    requirements: [
      '7+ years frontend experience',
      'Architecture experience',
      'Team leadership',
      'Modern frameworks',
    ],
    benefits: ['Leadership Role', 'Creative Projects', 'Mentoring', 'Competitive Salary'],
    tags: ['Leadership', 'Architecture', 'React', 'Mentoring'],
    isSaved: true,
    isApplied: false,
    companyLogo: '',
    companySize: '100-500',
    industry: 'Creative',
    urgency: 'high',
  },
  {
    id: 5,
    title: 'UI/UX Developer',
    company: 'DesignTech',
    location: 'Los Angeles, CA',
    type: 'Full-time',
    salary: '$90k - $120k',
    postedDate: '2024-01-19',
    matchScore: 82,
    isRemote: true,
    isHybrid: false,
    experience: 'Mid',
    description: 'Bridge the gap between design and development as a UI/UX Developer...',
    requirements: ['Frontend development skills', 'Design experience', 'Figma', 'User research'],
    benefits: ['Design Focus', 'Remote Work', 'Creative Freedom', 'Learning Opportunities'],
    tags: ['UI/UX', 'Frontend', 'Figma', 'Design'],
    isSaved: false,
    isApplied: false,
    companyLogo: '',
    companySize: '200-500',
    industry: 'Design',
    urgency: 'medium',
  },
  {
    id: 6,
    title: 'Mobile App Developer',
    company: 'AppGenius',
    location: 'Seattle, WA',
    type: 'Full-time',
    salary: '$110k - $140k',
    postedDate: '2024-01-21',
    matchScore: 79,
    isRemote: false,
    isHybrid: true,
    experience: 'Mid',
    description: 'Develop innovative mobile applications for iOS and Android platforms...',
    requirements: [
      'React Native',
      'iOS/Android development',
      'Mobile UI/UX',
      'App Store deployment',
    ],
    benefits: ['Mobile Focus', 'Innovation', 'Team Collaboration', 'Tech Stack Freedom'],
    tags: ['React Native', 'Mobile', 'iOS', 'Android'],
    isSaved: false,
    isApplied: false,
    companyLogo: '',
    companySize: '50-200',
    industry: 'Mobile',
    urgency: 'medium',
  },
];

const getMatchScoreColor = (score: number) => {
  if (score >= 90) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
  if (score >= 80) return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
  if (score >= 70) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
  return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
};

const getUrgencyIndicator = (urgency: string) => {
  switch (urgency) {
    case 'high':
      return <Badge className="bg-red-100 text-red-800">Hot</Badge>;
    case 'medium':
      return <Badge className="bg-yellow-100 text-yellow-800">Popular</Badge>;
    default:
      return null;
  }
};

export function JobSearchContent() {
  const { role } = useRole();

  // Redirect non-candidates away from job search
  if (role !== 'candidate') {
    return (
      <>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <div className="text-center">
            <h2 className="text-2xl font-bold">Access Restricted</h2>
            <p className="text-muted-foreground">Job search is only available to candidates.</p>
          </div>
        </div>
      </>
    );
  }

  const featuredJobs = mockJobs.filter((job) => job.matchScore >= 90);
  const recentJobs = mockJobs.filter((job) => {
    const postedDate = new Date(job.postedDate);
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
    return postedDate >= threeDaysAgo;
  });
  const remoteJobs = mockJobs.filter((job) => job.isRemote);

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Job Search</h1>
            <p className="text-muted-foreground">Discover your next career opportunity</p>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Available Jobs</CardTitle>
              <Search className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockJobs.length}</div>
              <p className="text-xs text-muted-foreground">+12 this week</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">High Match</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{featuredJobs.length}</div>
              <p className="text-xs text-muted-foreground">90%+ match</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Remote Jobs</CardTitle>
              <MapPin className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{remoteJobs.length}</div>
              <p className="text-xs text-muted-foreground">Work from anywhere</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New This Week</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{recentJobs.length}</div>
              <p className="text-xs text-muted-foreground">Fresh opportunities</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input placeholder="Search jobs, companies, or keywords..." className="pl-10" />
          </div>
          <Select defaultValue="all-locations">
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="Location" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-locations">All Locations</SelectItem>
              <SelectItem value="remote">Remote</SelectItem>
              <SelectItem value="san-francisco">San Francisco</SelectItem>
              <SelectItem value="new-york">New York</SelectItem>
              <SelectItem value="austin">Austin</SelectItem>
              <SelectItem value="seattle">Seattle</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="all-types">
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="Job Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-types">All Types</SelectItem>
              <SelectItem value="full-time">Full-time</SelectItem>
              <SelectItem value="part-time">Part-time</SelectItem>
              <SelectItem value="contract">Contract</SelectItem>
              <SelectItem value="internship">Internship</SelectItem>
            </SelectContent>
          </Select>
          <Select defaultValue="all-experience">
            <SelectTrigger className="w-full md:w-[180px]">
              <SelectValue placeholder="Experience" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all-experience">All Levels</SelectItem>
              <SelectItem value="entry">Entry Level</SelectItem>
              <SelectItem value="mid">Mid Level</SelectItem>
              <SelectItem value="senior">Senior Level</SelectItem>
              <SelectItem value="lead">Lead/Principal</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <Filter className="h-4 w-4" />
          </Button>
        </div>

        <Tabs defaultValue="all" className="w-full">
          <TabsList>
            <TabsTrigger value="all">All Jobs ({mockJobs.length})</TabsTrigger>
            <TabsTrigger value="featured">Featured ({featuredJobs.length})</TabsTrigger>
            <TabsTrigger value="recent">Recent ({recentJobs.length})</TabsTrigger>
            <TabsTrigger value="remote">Remote ({remoteJobs.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4 mt-6">
            {mockJobs.map((job) => (
              <Card key={job.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="text-lg font-semibold">{job.title}</h3>
                            {getUrgencyIndicator(job.urgency)}
                            {job.isApplied && (
                              <Badge className="bg-blue-100 text-blue-800">Applied</Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <Building className="h-4 w-4 text-muted-foreground" />
                            <span className="text-muted-foreground">{job.company}</span>
                            <Badge variant="outline">{job.companySize}</Badge>
                            <Badge variant="outline">{job.industry}</Badge>
                          </div>
                        </div>
                        <Badge className={getMatchScoreColor(job.matchScore)}>
                          {job.matchScore}% match
                        </Badge>
                      </div>

                      <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {job.location}
                          {job.isRemote && (
                            <Badge variant="outline" className="ml-1">
                              Remote
                            </Badge>
                          )}
                          {job.isHybrid && (
                            <Badge variant="outline" className="ml-1">
                              Hybrid
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          {job.salary}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Posted {job.postedDate}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {job.experience} Level
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{job.type}</Badge>
                        {job.tags.slice(0, 4).map((tag, index) => (
                          <Badge key={index} variant="outline">
                            {tag}
                          </Badge>
                        ))}
                        {job.tags.length > 4 && (
                          <Badge variant="outline">+{job.tags.length - 4} more</Badge>
                        )}
                      </div>

                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {job.description}
                      </p>

                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">Key Requirements:</h4>
                        <div className="flex flex-wrap gap-1">
                          {job.requirements.slice(0, 3).map((req, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {req}
                            </Badge>
                          ))}
                          {job.requirements.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{job.requirements.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 md:ml-4">
                      {!job.isApplied ? (
                        <Button>Apply Now</Button>
                      ) : (
                        <Button variant="outline" disabled>
                          Applied
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className={job.isSaved ? 'text-red-600' : ''}
                      >
                        {job.isSaved ? (
                          <>
                            <BookmarkCheck className="mr-2 h-4 w-4" />
                            Saved
                          </>
                        ) : (
                          <>
                            <Bookmark className="mr-2 h-4 w-4" />
                            Save
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="featured" className="space-y-4 mt-6">
            {featuredJobs.map((job) => (
              <Card
                key={job.id}
                className="hover:shadow-md transition-shadow border-l-4 border-l-green-500"
              >
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <Badge className="bg-green-100 text-green-800">Featured Match</Badge>
                  </div>
                  <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="text-lg font-semibold">{job.title}</h3>
                            {getUrgencyIndicator(job.urgency)}
                            {job.isApplied && (
                              <Badge className="bg-blue-100 text-blue-800">Applied</Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <Building className="h-4 w-4 text-muted-foreground" />
                            <span className="text-muted-foreground">{job.company}</span>
                            <Badge variant="outline">{job.companySize}</Badge>
                            <Badge variant="outline">{job.industry}</Badge>
                          </div>
                        </div>
                        <Badge className={getMatchScoreColor(job.matchScore)}>
                          {job.matchScore}% match
                        </Badge>
                      </div>

                      <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {job.location}
                          {job.isRemote && (
                            <Badge variant="outline" className="ml-1">
                              Remote
                            </Badge>
                          )}
                          {job.isHybrid && (
                            <Badge variant="outline" className="ml-1">
                              Hybrid
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          {job.salary}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Posted {job.postedDate}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {job.experience} Level
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{job.type}</Badge>
                        {job.tags.slice(0, 4).map((tag, index) => (
                          <Badge key={index} variant="outline">
                            {tag}
                          </Badge>
                        ))}
                        {job.tags.length > 4 && (
                          <Badge variant="outline">+{job.tags.length - 4} more</Badge>
                        )}
                      </div>

                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {job.description}
                      </p>

                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">Key Requirements:</h4>
                        <div className="flex flex-wrap gap-1">
                          {job.requirements.slice(0, 3).map((req, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {req}
                            </Badge>
                          ))}
                          {job.requirements.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{job.requirements.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 md:ml-4">
                      {!job.isApplied ? (
                        <Button>Apply Now</Button>
                      ) : (
                        <Button variant="outline" disabled>
                          Applied
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className={job.isSaved ? 'text-red-600' : ''}
                      >
                        {job.isSaved ? (
                          <>
                            <BookmarkCheck className="mr-2 h-4 w-4" />
                            Saved
                          </>
                        ) : (
                          <>
                            <Bookmark className="mr-2 h-4 w-4" />
                            Save
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="recent" className="space-y-4 mt-6">
            {recentJobs.map((job) => (
              <Card key={job.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <TrendingUp className="h-4 w-4 text-blue-500" />
                    <Badge className="bg-blue-100 text-blue-800">New</Badge>
                  </div>
                  <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="text-lg font-semibold">{job.title}</h3>
                            {getUrgencyIndicator(job.urgency)}
                            {job.isApplied && (
                              <Badge className="bg-blue-100 text-blue-800">Applied</Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <Building className="h-4 w-4 text-muted-foreground" />
                            <span className="text-muted-foreground">{job.company}</span>
                            <Badge variant="outline">{job.companySize}</Badge>
                            <Badge variant="outline">{job.industry}</Badge>
                          </div>
                        </div>
                        <Badge className={getMatchScoreColor(job.matchScore)}>
                          {job.matchScore}% match
                        </Badge>
                      </div>

                      <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {job.location}
                          {job.isRemote && (
                            <Badge variant="outline" className="ml-1">
                              Remote
                            </Badge>
                          )}
                          {job.isHybrid && (
                            <Badge variant="outline" className="ml-1">
                              Hybrid
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          {job.salary}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Posted {job.postedDate}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {job.experience} Level
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{job.type}</Badge>
                        {job.tags.slice(0, 4).map((tag, index) => (
                          <Badge key={index} variant="outline">
                            {tag}
                          </Badge>
                        ))}
                        {job.tags.length > 4 && (
                          <Badge variant="outline">+{job.tags.length - 4} more</Badge>
                        )}
                      </div>

                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {job.description}
                      </p>

                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">Key Requirements:</h4>
                        <div className="flex flex-wrap gap-1">
                          {job.requirements.slice(0, 3).map((req, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {req}
                            </Badge>
                          ))}
                          {job.requirements.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{job.requirements.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 md:ml-4">
                      {!job.isApplied ? (
                        <Button>Apply Now</Button>
                      ) : (
                        <Button variant="outline" disabled>
                          Applied
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className={job.isSaved ? 'text-red-600' : ''}
                      >
                        {job.isSaved ? (
                          <>
                            <BookmarkCheck className="mr-2 h-4 w-4" />
                            Saved
                          </>
                        ) : (
                          <>
                            <Bookmark className="mr-2 h-4 w-4" />
                            Save
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="remote" className="space-y-4 mt-6">
            {remoteJobs.map((job) => (
              <Card key={job.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <MapPin className="h-4 w-4 text-purple-500" />
                    <Badge className="bg-purple-100 text-purple-800">Remote</Badge>
                  </div>
                  <div className="flex flex-col space-y-4 md:flex-row md:items-start md:justify-between md:space-y-0">
                    <div className="space-y-3 flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="text-lg font-semibold">{job.title}</h3>
                            {getUrgencyIndicator(job.urgency)}
                            {job.isApplied && (
                              <Badge className="bg-blue-100 text-blue-800">Applied</Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-2 mt-1">
                            <Building className="h-4 w-4 text-muted-foreground" />
                            <span className="text-muted-foreground">{job.company}</span>
                            <Badge variant="outline">{job.companySize}</Badge>
                            <Badge variant="outline">{job.industry}</Badge>
                          </div>
                        </div>
                        <Badge className={getMatchScoreColor(job.matchScore)}>
                          {job.matchScore}% match
                        </Badge>
                      </div>

                      <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {job.location}
                          {job.isRemote && (
                            <Badge variant="outline" className="ml-1">
                              Remote
                            </Badge>
                          )}
                          {job.isHybrid && (
                            <Badge variant="outline" className="ml-1">
                              Hybrid
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          {job.salary}
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Posted {job.postedDate}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {job.experience} Level
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{job.type}</Badge>
                        {job.tags.slice(0, 4).map((tag, index) => (
                          <Badge key={index} variant="outline">
                            {tag}
                          </Badge>
                        ))}
                        {job.tags.length > 4 && (
                          <Badge variant="outline">+{job.tags.length - 4} more</Badge>
                        )}
                      </div>

                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {job.description}
                      </p>

                      <div className="space-y-2">
                        <h4 className="text-sm font-medium">Key Requirements:</h4>
                        <div className="flex flex-wrap gap-1">
                          {job.requirements.slice(0, 3).map((req, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {req}
                            </Badge>
                          ))}
                          {job.requirements.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{job.requirements.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 md:ml-4">
                      {!job.isApplied ? (
                        <Button>Apply Now</Button>
                      ) : (
                        <Button variant="outline" disabled>
                          Applied
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className={job.isSaved ? 'text-red-600' : ''}
                      >
                        {job.isSaved ? (
                          <>
                            <BookmarkCheck className="mr-2 h-4 w-4" />
                            Saved
                          </>
                        ) : (
                          <>
                            <Bookmark className="mr-2 h-4 w-4" />
                            Save
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
