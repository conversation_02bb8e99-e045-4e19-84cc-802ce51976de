'use client';

import {
  IconAlertCircle,
  IconBell,
  IconBriefcase,
  IconCalendar,
  IconCheck,
  IconCircleCheck,
  IconSettings,
  IconTrash,
  IconUser,
  IconX,
} from '@tabler/icons-react';
import { InfoIcon as IconInfo } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';

import { useRole } from '../../../../../../contexts/role-context';

interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  category: string;
}

export function NotificationsContent() {
  const { role } = useRole();

  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'info',
      title: 'New Application Received',
      message: '<PERSON> Doe applied for Senior Developer position',
      timestamp: '2 hours ago',
      read: false,
      category: 'applications',
    },
    {
      id: '2',
      type: 'success',
      title: 'Interview Scheduled',
      message: 'Interview with Sarah Johnson scheduled for tomorrow at 2:00 PM',
      timestamp: '4 hours ago',
      read: false,
      category: 'interviews',
    },
    {
      id: '3',
      type: 'warning',
      title: 'Profile Incomplete',
      message: 'Please complete your profile to improve visibility',
      timestamp: '1 day ago',
      read: true,
      category: 'profile',
    },
    {
      id: '4',
      type: 'info',
      title: 'New Job Posted',
      message: 'Frontend Developer position posted successfully',
      timestamp: '2 days ago',
      read: true,
      category: 'jobs',
    },
  ]);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <IconCircleCheck className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <IconAlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <IconX className="h-5 w-5 text-red-500" />;
      default:
        return <IconInfo className="h-5 w-5 text-blue-500" />;
    }
  };

  const markAsRead = (id: string) => {
    setNotifications((prev) =>
      prev.map((notif) => (notif.id === id ? { ...notif, read: true } : notif))
    );
  };

  const deleteNotification = (id: string) => {
    setNotifications((prev) => prev.filter((notif) => notif.id !== id));
  };

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((notif) => ({ ...notif, read: true })));
  };

  const unreadCount = notifications.filter((n) => !n.read).length;

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <h2 className="text-2xl font-bold tracking-tight">Notifications</h2>
          <p className="text-muted-foreground">Stay updated with your latest activities</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline">{unreadCount} unread</Badge>
          {unreadCount > 0 && (
            <Button size="sm" variant="outline" onClick={markAllAsRead}>
              <IconCheck className="h-4 w-4 mr-2" />
              Mark all as read
            </Button>
          )}
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        {/* Notification Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <IconSettings className="h-5 w-5" />
              Notification Settings
            </CardTitle>
            <CardDescription>Configure how you receive notifications</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="email-notifications">Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">Receive updates via email</p>
                </div>
                <Switch id="email-notifications" defaultChecked />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="push-notifications">Push Notifications</Label>
                  <p className="text-sm text-muted-foreground">Browser notifications</p>
                </div>
                <Switch id="push-notifications" defaultChecked />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="sms-notifications">SMS Notifications</Label>
                  <p className="text-sm text-muted-foreground">Important updates only</p>
                </div>
                <Switch id="sms-notifications" />
              </div>
            </div>

            <Separator />

            <div className="space-y-3">
              <h4 className="font-medium">Categories</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="applications">Applications</Label>
                  <Switch id="applications" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="interviews">Interviews</Label>
                  <Switch id="interviews" defaultChecked />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="jobs">Job Updates</Label>
                  <Switch id="jobs" defaultChecked />
                </div>
                {role !== 'candidate' && (
                  <div className="flex items-center justify-between">
                    <Label htmlFor="system">System Updates</Label>
                    <Switch id="system" />
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notifications List */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <IconBell className="h-5 w-5" />
              Recent Notifications
            </CardTitle>
            <CardDescription>Your latest notifications and updates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {notifications.length === 0 ? (
                <div className="text-center py-8">
                  <IconBell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No notifications yet</p>
                </div>
              ) : (
                notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`flex items-start gap-3 p-4 rounded-lg border transition-colors ${
                      !notification.read ? 'bg-primary/5 border-primary/20' : 'bg-muted/30'
                    }`}
                  >
                    <div className="mt-0.5">{getNotificationIcon(notification.type)}</div>

                    <div className="flex-1 space-y-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">{notification.title}</h4>
                        {!notification.read && <div className="h-2 w-2 bg-primary rounded-full" />}
                      </div>
                      <p className="text-sm text-muted-foreground">{notification.message}</p>
                      <p className="text-xs text-muted-foreground">{notification.timestamp}</p>
                    </div>

                    <div className="flex items-center gap-1">
                      {!notification.read && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => markAsRead(notification.id)}
                        >
                          <IconCheck className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => deleteNotification(notification.id)}
                      >
                        <IconTrash className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Role-specific notification info */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Types</CardTitle>
          <CardDescription>
            Types of notifications you might receive based on your role
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {role === 'admin' && (
              <>
                <div className="flex items-center gap-3 p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20">
                  <IconUser className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="font-medium">User Activity</p>
                    <p className="text-sm text-muted-foreground">New registrations, role changes</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-lg bg-orange-50 dark:bg-orange-950/20">
                  <IconSettings className="h-5 w-5 text-orange-500" />
                  <div>
                    <p className="font-medium">System Updates</p>
                    <p className="text-sm text-muted-foreground">Maintenance, security alerts</p>
                  </div>
                </div>
              </>
            )}

            {(role === 'admin' || role === 'recruiter') && (
              <>
                <div className="flex items-center gap-3 p-3 rounded-lg bg-teal-50 dark:bg-teal-950/20">
                  <IconBriefcase className="h-5 w-5 text-teal-500" />
                  <div>
                    <p className="font-medium">Applications</p>
                    <p className="text-sm text-muted-foreground">
                      New applications, status updates
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-lg bg-pink-50 dark:bg-pink-950/20">
                  <IconCalendar className="h-5 w-5 text-pink-500" />
                  <div>
                    <p className="font-medium">Interviews</p>
                    <p className="text-sm text-muted-foreground">Scheduled interviews, reminders</p>
                  </div>
                </div>
              </>
            )}

            {role === 'candidate' && (
              <>
                <div className="flex items-center gap-3 p-3 rounded-lg bg-green-50 dark:bg-green-950/20">
                  <IconBriefcase className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="font-medium">Job Matches</p>
                    <p className="text-sm text-muted-foreground">New job recommendations</p>
                  </div>
                </div>
                <div className="flex items-center gap-3 p-3 rounded-lg bg-purple-50 dark:bg-purple-950/20">
                  <IconCalendar className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="font-medium">Interview Invites</p>
                    <p className="text-sm text-muted-foreground">Interview requests and updates</p>
                  </div>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
