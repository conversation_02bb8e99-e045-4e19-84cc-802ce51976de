'use client';

import {
  IconBell,
  IconBuilding,
  IconPalette,
  IconSettings,
  IconShield,
  IconUser,
  IconUsers,
} from '@tabler/icons-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';

import { useRole } from '../../../../../../contexts/role-context';

export function SettingsContent() {
  const { role } = useRole();

  const getSettingsSections = () => {
    const common = [
      {
        id: 'profile',
        title: 'Profile Settings',
        description: 'Manage your personal information and account details',
        icon: IconUser,
        color: 'text-blue-500',
        bgColor: 'bg-blue-500/10',
      },
      {
        id: 'notifications',
        title: 'Notifications',
        description: 'Configure how you receive notifications',
        icon: IconBell,
        color: 'text-yellow-500',
        bgColor: 'bg-yellow-500/10',
      },
      {
        id: 'security',
        title: 'Security & Privacy',
        description: 'Manage your security and privacy preferences',
        icon: IconShield,
        color: 'text-green-500',
        bgColor: 'bg-green-500/10',
      },
      {
        id: 'appearance',
        title: 'Appearance',
        description: 'Customize your interface and theme preferences',
        icon: IconPalette,
        color: 'text-purple-500',
        bgColor: 'bg-purple-500/10',
      },
    ];

    if (role === 'admin') {
      return [
        ...common,
        {
          id: 'organization',
          title: 'Organization Settings',
          description: 'Manage organization-wide settings and policies',
          icon: IconBuilding,
          color: 'text-orange-500',
          bgColor: 'bg-orange-500/10',
        },
        {
          id: 'users',
          title: 'User Management',
          description: 'Manage users, roles, and permissions',
          icon: IconUsers,
          color: 'text-teal-500',
          bgColor: 'bg-teal-500/10',
        },
        {
          id: 'system',
          title: 'System Settings',
          description: 'Advanced system configuration and maintenance',
          icon: IconSettings,
          color: 'text-red-500',
          bgColor: 'bg-red-500/10',
        },
      ];
    } else if (role === 'recruiter') {
      return [
        ...common,
        {
          id: 'recruitment',
          title: 'Recruitment Settings',
          description: 'Configure your recruitment preferences and workflows',
          icon: IconBuilding,
          color: 'text-orange-500',
          bgColor: 'bg-orange-500/10',
        },
      ];
    } else {
      return common;
    }
  };

  const sections = getSettingsSections();

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:gap-6 md:p-6">
      {/* Header */}
      <div className="space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">Settings</h2>
        <p className="text-muted-foreground">Manage your account settings and preferences</p>
      </div>

      {/* Role Badge */}
      <div className="flex items-center gap-2">
        <Badge variant="outline" className="capitalize">
          {role} Account
        </Badge>
      </div>

      {/* Settings Sections */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {sections.map((section) => (
          <Card
            key={section.id}
            className="relative overflow-hidden transition-all duration-200 hover:shadow-md group cursor-pointer"
          >
            <CardHeader className="pb-3">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-md ${section.bgColor}`}>
                  <section.icon className={`h-5 w-5 ${section.color}`} />
                </div>
                <div>
                  <CardTitle className="text-base">{section.title}</CardTitle>
                  <CardDescription className="text-sm">{section.description}</CardDescription>
                </div>
              </div>
            </CardHeader>

            {/* Gradient overlay */}
            <div
              className={`absolute inset-0 bg-gradient-to-br ${section.bgColor} opacity-0 transition-opacity duration-200 group-hover:opacity-5`}
            />
          </Card>
        ))}
      </div>

      {/* Quick Settings */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Quick Settings</CardTitle>
            <CardDescription>Common settings you might want to adjust</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="email-notifications">Email Notifications</Label>
                <p className="text-sm text-muted-foreground">Receive notifications via email</p>
              </div>
              <Switch id="email-notifications" defaultChecked />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="push-notifications">Push Notifications</Label>
                <p className="text-sm text-muted-foreground">
                  Receive push notifications in browser
                </p>
              </div>
              <Switch id="push-notifications" defaultChecked />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="dark-mode">Dark Mode</Label>
                <p className="text-sm text-muted-foreground">Use dark theme</p>
              </div>
              <Switch id="dark-mode" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Account Information</CardTitle>
            <CardDescription>Update your account details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="display-name">Display Name</Label>
              <Input id="display-name" placeholder="Your display name" defaultValue="John Doe" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                defaultValue="<EMAIL>"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="timezone">Timezone</Label>
              <Input
                id="timezone"
                placeholder="Your timezone"
                defaultValue="UTC-5 (Eastern Time)"
              />
            </div>

            <Button className="w-full">Save Changes</Button>
          </CardContent>
        </Card>
      </div>

      {/* Role-specific settings */}
      {role === 'admin' && (
        <Card>
          <CardHeader>
            <CardTitle>Admin Controls</CardTitle>
            <CardDescription>Administrative settings and controls</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <Button variant="outline" className="h-auto p-4 flex flex-col items-start gap-2">
                <IconUsers className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="font-medium">Manage Users</p>
                  <p className="text-sm text-muted-foreground">Add, edit, or remove users</p>
                </div>
              </Button>

              <Button variant="outline" className="h-auto p-4 flex flex-col items-start gap-2">
                <IconBuilding className="h-5 w-5 text-orange-500" />
                <div>
                  <p className="font-medium">Organizations</p>
                  <p className="text-sm text-muted-foreground">Manage organization settings</p>
                </div>
              </Button>

              <Button variant="outline" className="h-auto p-4 flex flex-col items-start gap-2">
                <IconSettings className="h-5 w-5 text-purple-500" />
                <div>
                  <p className="font-medium">System Config</p>
                  <p className="text-sm text-muted-foreground">Advanced system settings</p>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
