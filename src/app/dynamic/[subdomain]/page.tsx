import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { api } from '@/trpc/server';

interface ProfilePageProps {
  params: Promise<{
    subdomain: string;
  }>;
}

export default async function ProfilePage(props: ProfilePageProps) {
  const params = await props.params;
  const subdomain = params.subdomain;
  const data = await api.organization.getPublicOrganizationDetailsBySlug(subdomain);
  if (!data) {
    return (
      <div className="flex flex-col items-center justify-center h-screen ">
        <h1 className="text-3xl font-bold">Organization Not Found</h1>
        <p className="text-gray-600 mt-2">
          The organization with the specified subdomain does not exist.
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center gap-4 p-4 md:gap-6 md:p-6 bg-white dark:bg-gray-800 shadow-md rounded-lg max-w-4xl mx-auto mt-10">
      <div className="flex flex-col items-center gap-4">
        <div className="flex items-center justify-center">
          <Avatar className="w-20 h-20">
            <AvatarFallback className="text-2xl">
              {data.name.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
        </div>
        <h1 className="text-3xl font-bold text-blue-600 dark:text-blue-400 text-center">
          {data.name}
        </h1>
      </div>
      <p className="text-gray-600 dark:text-gray-300 text-center">
        {data.description || 'No description available.'}
      </p>
    </div>
  );
}
