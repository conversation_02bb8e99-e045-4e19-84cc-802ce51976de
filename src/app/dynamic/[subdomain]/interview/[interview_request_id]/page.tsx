import { <PERSON>, CheckCircle, Clock, Mic, Monitor, Play, Users, Video } from 'lucide-react';
import Link from 'next/link';

import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

export default function InterviewPage() {
  return (
    <div className="min-h-screen flex items-center justify-center p-2 bg-gray-50 dark:bg-gray-900">
      <Card className="max-w-4xl w-full mx-2 p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-blue-600 dark:bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <Video className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mb-2">
            AI Video Interview
          </h1>
          <p className="text-base text-gray-600 dark:text-gray-300">
            Professional interview session with AI assistant
          </p>
        </div>

        {/* Session Info */}
        <div className="grid md:grid-cols-3 gap-3 mb-6">
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <Users size={18} className="text-blue-500 dark:text-blue-400" />
            <span>You + AI Interviewer</span>
          </div>
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <Clock size={18} className="text-blue-500 dark:text-blue-400" />
            <span>20 minute session</span>
          </div>
          <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <CheckCircle size={18} className="text-green-500 dark:text-green-400" />
            <span>Auto-recorded</span>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-6 mb-6">
          {/* Interview Features */}
          <div>
            <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-3">
              Interview Features
            </h2>
            <div className="grid gap-3">
              <div className="flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <Video className="w-6 h-6 text-blue-600 dark:text-blue-400 mt-1" />
                <div>
                  <h3 className="font-medium text-gray-800 dark:text-gray-100">Video Recording</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Combined audio & video recording for review
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <Mic className="w-6 h-6 text-green-600 dark:text-green-400 mt-1" />
                <div>
                  <h3 className="font-medium text-gray-800 dark:text-gray-100">
                    Live Transcription
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Real-time speech-to-text conversion
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <Monitor className="w-6 h-6 text-purple-600 dark:text-purple-400 mt-1" />
                <div>
                  <h3 className="font-medium text-gray-800 dark:text-gray-100">Screen Sharing</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Share presentations or portfolios
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <Camera className="w-6 h-6 text-orange-600 dark:text-orange-400 mt-1" />
                <div>
                  <h3 className="font-medium text-gray-800 dark:text-gray-100">Snapshots</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    Capture important moments during interview
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Pre-Interview Checklist */}
          <div>
            <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-3">
              Before You Start
            </h2>
            <div className="space-y-2">
              <div className="flex items-center space-x-3 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400" />
                <span className="text-gray-700 dark:text-gray-200">
                  Ensure stable internet connection
                </span>
              </div>
              <div className="flex items-center space-x-3 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400" />
                <span className="text-gray-700 dark:text-gray-200">
                  Test your camera and microphone
                </span>
              </div>
              <div className="flex items-center space-x-3 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400" />
                <span className="text-gray-700 dark:text-gray-200">
                  Find a quiet, well-lit environment
                </span>
              </div>
              <div className="flex items-center space-x-3 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-500 dark:text-green-400" />
                <span className="text-gray-700 dark:text-gray-200">
                  Prepare your resume and portfolio (optional)
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Start Button */}
        <Link href={'/interview/session'}>
          <Button
            className="w-full bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-base py-4 flex items-center justify-center gap-2"
            size="lg"
          >
            <Play className="w-5 h-5" />
            Start Interview Session
          </Button>
        </Link>

        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            By proceeding, you agree to allow camera and microphone access for the interview
            session.
          </p>
          <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
            Recording will start automatically and can be downloaded after completion.
          </p>
        </div>
      </Card>
    </div>
  );
}
