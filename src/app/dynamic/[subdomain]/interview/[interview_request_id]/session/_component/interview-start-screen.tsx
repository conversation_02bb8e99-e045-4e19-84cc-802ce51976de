import { Camera, Play } from 'lucide-react';
import React from 'react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

interface InterviewStartScreenProps {
  onStartInterview: () => void;
  onCancel: () => void;
}

/**
 * Interview Start Screen Component
 * Displays welcome message and instructions before starting the interview
 */
const InterviewStartScreen: React.FC<InterviewStartScreenProps> = ({
  onStartInterview,
  onCancel,
}) => {
  return (
    <div className="h-screen bg-gray-50 dark:bg-gray-900 text-gray-800 dark:text-white flex items-center justify-center p-2">
      <Card className="max-w-2xl mx-auto p-8 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
        <div className="text-center space-y-6">
          {/* Header Icon */}
          <div className="w-20 h-20 mx-auto bg-blue-600 dark:bg-blue-500 rounded-full flex items-center justify-center">
            <Camera size={32} className="text-white" />
          </div>

          {/* Title */}
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
            Video Interview Session
          </h1>

          {/* Welcome Message */}
          <div className="space-y-4 text-gray-600 dark:text-gray-300">
            <p className="text-lg">Welcome to your video interview session!</p>

            {/* Instructions List */}
            <div className="text-left space-y-3">
              <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-3">
                Before we begin:
              </h3>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start space-x-2">
                  <span className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  <span>Ensure you&apos;re in a quiet, well-lit environment</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  <span>Check your camera and microphone are working properly</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  <span>Have your resume and relevant documents ready</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  <span>The session will be recorded for evaluation purposes</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  <span>You can use screen sharing to present your work if needed</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full mt-2 flex-shrink-0"></span>
                  <span>Take your time to think before answering questions</span>
                </li>
              </ul>
            </div>

            {/* Duration Info */}
            <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-700">
              <p className="text-blue-700 dark:text-blue-200 text-sm">
                <strong>Duration:</strong> This interview session is scheduled for 20 minutes.
                Please be concise but thorough in your responses.
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-4 justify-center pt-4">
            <Button
              onClick={onCancel}
              variant="outline"
              className="border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button
              onClick={onStartInterview}
              className="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white px-8 py-3 text-lg"
            >
              <Play size={20} className="mr-2" />
              Start Interview
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default InterviewStartScreen;
