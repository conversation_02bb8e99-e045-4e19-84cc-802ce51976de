# Video Call Component Architecture

This directory contains the refactored video call functionality, broken down into smaller, manageable components and custom hooks.

## New Layout Design

### Desktop Layout:

- **Left Side**: User video feed (full height)
  - **Top Right Corner**: AI video feed overlay
  - **Bottom Right Corner**: Screen share window (when active)
- **Right Side**: Chat transcript sidebar (fixed width)
- **Bottom**: Controls panel (always visible, separate from content)

### Mobile Layout:

- **Main Area**: User video feed (full screen)
  - **Top Right Corner**: AI video feed overlay (smaller)
  - **Bottom Right Corner**: Screen share window (when active, smaller)
- **Chat**: Toggle-able overlay (accessed via Chat button in controls)
- **Bottom**: Controls panel with Chat toggle button

### Key Layout Features:

- ✅ **No Scrolling**: Everything fits in viewport
- ✅ **Fixed Positioning**: AI video always stays in top-right corner
- ✅ **Independent Overlays**: Screen share and AI video don't interfere with each other
- ✅ **Picture-in-Picture**: AI and screen share overlays over user video
- ✅ **Mobile Friendly**: Responsive design with mobile-specific features
- ✅ **Fixed Controls**: Always accessible at bottom

### Video Layout Hierarchy:

1. **User Video**: Always full screen/area (base layer)
2. **AI Video**: Always top-right corner (highest z-index: 20)
3. **Screen Share**: Always bottom-right corner when active (z-index: 15)
4. **Status Indicators**: Top-left corner (z-index: 10)

## Layout Specifications

### 📱 Mobile Layout (< 768px)

```
┌─────────────────────────────────┐
│ Header (Timer + Status)         │
├─────────────────────────────────┤
│                                 │
│     User Video (Full Screen)    │
│                                 │
│  ┌─────────┐                   │
│  │AI Video │                   │
│  │(Top R)  │                   │
│  └─────────┘                   │
│                                 │
│              ┌──────────────┐   │
│              │Screen Share  │   │
│              │(Bottom R)    │   │
│              └──────────────┘   │
├─────────────────────────────────┤
│ Controls Panel + Chat Toggle    │
└─────────────────────────────────┘
```

**Chat Overlay (when toggled):**

- Appears over video area
- Dismissible with X button
- Scrollable transcript content

### 🖥️ Desktop Layout (≥ 768px)

```
┌─────────────────────────────────────────────────┐
│ Header (Timer + Status Indicators)             │
├─────────────────────┬───────────────────────────┤
│                     │                           │
│  User Video         │                           │
│                     │      Chat Transcript      │
│                     │        (Sidebar)          │
│                     │                           │
│ ┌─────────┐         │                           │
│ │AI Video │         │                           │
│ │(Top R)  │         │                           │
│ └─────────┘         │                           │
│                     │                           │
│     ┌──────────┐    │                           │
│     │Screen    │    │                           │
│     │Share     │    │                           │
│     │(Bottom R)│    │                           │
│     └──────────┘    │                           │
├─────────────────────┴───────────────────────────┤
│           Controls Panel (Centered)             │
└─────────────────────────────────────────────────┘
```

## ✨ Key Features

### Layout Features:

- ✅ **No Scrolling**: Everything fits in viewport height
- ✅ **Responsive Design**: Adapts seamlessly to all screen sizes
- ✅ **Fixed Controls**: Always accessible at the bottom
- ✅ **Picture-in-Picture**: AI and screen share overlays
- ✅ **Smart Positioning**: AI video moves when screen sharing

### Mobile Enhancements:

- 📱 **Chat Toggle**: Button in controls to show/hide transcript
- 📱 **Overlay Design**: Chat appears as dismissible overlay
- 📱 **Touch Friendly**: Larger touch targets for buttons
- 📱 **Optimized Sizes**: Smaller overlay windows for mobile

### Status Indicators:

- 🔴 **Mute Status**: Red indicator when muted
- 🔴 **Recording Status**: Animated recording indicator
- 🔵 **Screen Share**: Blue border and label
- ⏱️ **Timer**: Interview duration in header

### Accessibility:

- ♿ **Keyboard Navigation**: All controls accessible via keyboard
- ♿ **Screen Reader**: Proper ARIA labels and semantic HTML
- ♿ **High Contrast**: Clear visual hierarchy and contrast
- ♿ **Focus Management**: Visible focus indicators

### Performance:

- ⚡ **Optimized Rendering**: No unnecessary re-renders
- ⚡ **Efficient Layouts**: CSS Grid and Flexbox
- ⚡ **Memory Management**: Proper cleanup of streams
- ⚡ **Smooth Animations**: Hardware-accelerated transitions

## Structure

### Main Components

- **`video-call.tsx`** - Main orchestrator component that brings everything together
- **`interview-start-screen.tsx`** - Welcome screen shown before interview starts
- **`video-area.tsx`** - Handles video display for both mobile and desktop layouts

### Custom Hooks (in `/hooks` directory)

- **`use-media-stream.ts`** - Manages camera/microphone access and controls
- **`use-screen-share.ts`** - Handles screen sharing functionality
- **`use-recording.ts`** - Manages both camera and screen recording
- **`use-speech-recognition.ts`** - Handles speech-to-text and transcript management
- **`use-snapshot.ts`** - Captures snapshots from video streams

### Existing Components (unchanged)

- **`ai-video-feed.tsx`** - AI interviewer video display
- **`user-video-feed.tsx`** - User camera video display
- **`chat-transcript.tsx`** - Chat/transcript sidebar
- **`media-control.tsx`** - Media control buttons
- **`timer-display.tsx`** - Interview timer
- **`device-selector.tsx`** - Device selection modal

## Key Benefits of This Architecture

### 1. **Separation of Concerns**

Each hook handles a specific aspect:

- Media stream management
- Recording functionality
- Screen sharing
- Speech recognition
- Snapshot capture

### 2. **Reusability**

Hooks can be reused in other components if needed.

### 3. **Testability**

Each hook can be tested independently.

### 4. **Maintainability**

Easier to debug and modify specific functionality.

### 5. **Code Organization**

Related functionality is grouped together.

## Hook Usage Examples

### Media Stream Hook

```typescript
const {
  mediaStream,
  isMuted,
  isCameraOff,
  toggleMute,
  toggleCamera,
  handleDeviceChange,
  stopMediaStream,
} = useMediaStream(isInterviewStarted);
```

### Recording Hook

```typescript
const {
  isCameraRecording,
  startCameraRecording,
  stopCameraRecording,
  downloadCameraRecording,
  // ... screen recording methods
} = useRecording();
```

### Screen Share Hook

```typescript
const { screenStream, isScreenSharing, toggleScreenShare, stopScreenShare } = useScreenShare();
```

### Speech Recognition Hook

```typescript
const { transcriptMessages, stopRecognition, addMessage } = useSpeechRecognition(
  isInterviewStarted,
  isMuted
);
```

### Snapshot Hook

```typescript
const { takeSnapshot, canvasRef } = useSnapshot();
```

## Component Flow

1. **Interview Start Screen** - Shows welcome message and instructions
2. **Main Video Call** - Orchestrates all functionality using hooks
3. **Video Area** - Handles video layout (mobile/desktop)
4. **Media Controls** - Provides user interaction buttons
5. **Chat Transcript** - Shows conversation history

## State Management

The main `VideoCall` component maintains:

- Interview flow state (`isInterviewStarted`)
- UI state (`showDeviceSelector`, speaking indicators)
- Video element refs

Each hook manages its own specific state and provides methods to interact with it.

## Error Handling

Each hook includes:

- Console logging for debugging
- Error catching for media access
- Graceful degradation when features aren't supported

## Performance Considerations

- Hooks use `useCallback` to prevent unnecessary re-renders
- Media streams are properly cleaned up
- Event listeners are removed on unmount
- Recording chunks are efficiently managed
