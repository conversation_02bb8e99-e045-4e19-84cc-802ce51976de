'use client';

import { AlertCircle, Home, RotateCcw } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect } from 'react';

import { AuthCard } from '@/components/ui/auth-card';
import { Button } from '@/components/ui/button';
import { RadialGradientBackground } from '@/components/ui/radial-gradient-background';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error(error);
  }, [error]);

  return (
    <RadialGradientBackground>
      <div className="flex w-full max-w-md flex-col gap-8">
        {/* Logo section */}
        <div className="text-center">
          <Image
            src="/images/hirelytics-full-logo.svg"
            alt="Hirelytics"
            width={200}
            height={180}
            className="mx-auto"
          />
        </div>

        <div className="flex flex-col gap-6">
          <AuthCard
            title="Something went wrong"
            description="An unexpected error occurred. Please try again."
          >
            <div className="flex flex-col items-center gap-6">
              {/* Error Icon */}
              <div className="flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r from-red-100 to-pink-100 dark:from-red-900/30 dark:to-pink-900/30">
                <AlertCircle className="w-10 h-10 text-red-600 dark:text-red-400" />
              </div>

              {/* Error Message */}
              <div className="text-center">
                <h1 className="text-4xl font-bold bg-gradient-to-r from-red-600 to-pink-600 dark:from-red-400 dark:to-pink-400 bg-clip-text text-transparent mb-2">
                  Oops!
                </h1>
                <p className="text-muted-foreground text-sm">
                  {error.digest && (
                    <span className="block text-xs text-muted-foreground/70 mt-2">
                      Error ID: {error.digest}
                    </span>
                  )}
                </p>
              </div>

              {/* Action Buttons */}
              <div className="grid gap-4 w-full">
                <div className="flex flex-col gap-4">
                  <Button
                    onClick={() => reset()}
                    variant="outline"
                    className="w-full h-12 border-2 border-gray-200 dark:border-gray-600 hover:border-red-300 dark:hover:border-red-500 hover:bg-red-50 dark:hover:bg-red-950/50 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-[1.02]"
                  >
                    <RotateCcw className="size-5 mr-2" />
                    Try Again
                  </Button>
                  <Link href="/">
                    <Button className="w-full h-12 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 dark:from-blue-500 dark:to-blue-600 dark:hover:from-blue-600 dark:hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] text-white">
                      <Home className="size-5 mr-2" />
                      Go Home
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </AuthCard>
        </div>
      </div>
    </RadialGradientBackground>
  );
}
