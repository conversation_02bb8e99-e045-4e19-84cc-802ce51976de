import {
  Briefcase,
  Building2,
  Car,
  Code,
  Coffee,
  DollarSign,
  Factory,
  GraduationCap,
  Heart,
  Home,
  type LucideIcon,
  Shield,
  ShoppingCart,
  TreePine,
  Truck,
  Tv,
  Users,
  Zap,
} from 'lucide-react';

export interface Industry {
  value: string;
  label: string;
  description?: string;
  icon?: LucideIcon;
}

export const industries: Industry[] = [
  {
    value: 'technology',
    label: 'Technology',
    description: 'Software, Hardware, IT Services',
    icon: Code,
  },
  {
    value: 'healthcare',
    label: 'Healthcare',
    description: 'Medical, Pharmaceutical, Biotech',
    icon: Heart,
  },
  {
    value: 'finance',
    label: 'Finance & Banking',
    description: 'Banking, Insurance, Investment',
    icon: DollarSign,
  },
  {
    value: 'education',
    label: 'Education',
    description: 'Schools, Universities, Training',
    icon: GraduationCap,
  },
  {
    value: 'manufacturing',
    label: 'Manufacturing',
    description: 'Production, Assembly, Industrial',
    icon: Factory,
  },
  {
    value: 'retail',
    label: 'Retail & E-commerce',
    description: 'Sales, Customer Service, Logistics',
    icon: ShoppingCart,
  },
  {
    value: 'hospitality',
    label: 'Hospitality & Tourism',
    description: 'Hotels, Restaurants, Travel',
    icon: Coffee,
  },
  {
    value: 'consulting',
    label: 'Consulting',
    description: 'Business, Management, Strategy',
    icon: Users,
  },
  {
    value: 'media',
    label: 'Media & Entertainment',
    description: 'Content, Broadcasting, Gaming',
    icon: Tv,
  },
  {
    value: 'automotive',
    label: 'Automotive',
    description: 'Vehicle Manufacturing, Parts',
    icon: Car,
  },
  {
    value: 'real-estate',
    label: 'Real Estate',
    description: 'Property, Construction, Development',
    icon: Home,
  },
  {
    value: 'energy',
    label: 'Energy & Utilities',
    description: 'Oil, Gas, Renewable Energy',
    icon: Zap,
  },
  {
    value: 'transportation',
    label: 'Transportation & Logistics',
    description: 'Shipping, Delivery, Supply Chain',
    icon: Truck,
  },
  {
    value: 'government',
    label: 'Government & Public Sector',
    description: 'Public Service, Defense',
    icon: Shield,
  },
  {
    value: 'nonprofit',
    label: 'Non-profit & NGO',
    description: 'Charity, Social Services',
    icon: Heart,
  },
  {
    value: 'agriculture',
    label: 'Agriculture & Food',
    description: 'Farming, Food Production',
    icon: TreePine,
  },
  {
    value: 'legal',
    label: 'Legal Services',
    description: 'Law Firms, Legal Consulting',
    icon: Briefcase,
  },
  {
    value: 'marketing',
    label: 'Marketing & Advertising',
    description: 'Digital Marketing, PR, Branding',
    icon: Tv,
  },
  {
    value: 'other',
    label: 'Other',
    description: 'Other industries not listed',
    icon: Building2,
  },
];

// Skills mapped to industries
export const skillsByIndustry = {
  technology: [
    // Programming Languages
    { value: 'javascript', label: 'JavaScript' },
    { value: 'typescript', label: 'TypeScript' },
    { value: 'python', label: 'Python' },
    { value: 'java', label: 'Java' },
    { value: 'csharp', label: 'C#' },
    { value: 'cpp', label: 'C++' },
    { value: 'go', label: 'Go' },
    { value: 'rust', label: 'Rust' },
    { value: 'php', label: 'PHP' },
    { value: 'ruby', label: 'Ruby' },
    { value: 'swift', label: 'Swift' },
    { value: 'kotlin', label: 'Kotlin' },

    // Frontend
    { value: 'react', label: 'React' },
    { value: 'angular', label: 'Angular' },
    { value: 'vue', label: 'Vue.js' },
    { value: 'nextjs', label: 'Next.js' },
    { value: 'html', label: 'HTML' },
    { value: 'css', label: 'CSS' },
    { value: 'sass', label: 'SASS/SCSS' },
    { value: 'tailwind', label: 'Tailwind CSS' },
    { value: 'bootstrap', label: 'Bootstrap' },

    // Backend
    { value: 'nodejs', label: 'Node.js' },
    { value: 'express', label: 'Express.js' },
    { value: 'django', label: 'Django' },
    { value: 'flask', label: 'Flask' },
    { value: 'spring', label: 'Spring Framework' },
    { value: 'dotnet', label: '.NET' },
    { value: 'laravel', label: 'Laravel' },
    { value: 'rails', label: 'Ruby on Rails' },

    // Databases
    { value: 'mysql', label: 'MySQL' },
    { value: 'postgresql', label: 'PostgreSQL' },
    { value: 'mongodb', label: 'MongoDB' },
    { value: 'redis', label: 'Redis' },
    { value: 'elasticsearch', label: 'Elasticsearch' },
    { value: 'oracle', label: 'Oracle Database' },
    { value: 'sqlite', label: 'SQLite' },

    // DevOps & Cloud
    { value: 'aws', label: 'Amazon Web Services' },
    { value: 'azure', label: 'Microsoft Azure' },
    { value: 'gcp', label: 'Google Cloud Platform' },
    { value: 'docker', label: 'Docker' },
    { value: 'kubernetes', label: 'Kubernetes' },
    { value: 'jenkins', label: 'Jenkins' },
    { value: 'git', label: 'Git' },
    { value: 'github', label: 'GitHub' },
    { value: 'gitlab', label: 'GitLab' },
    { value: 'terraform', label: 'Terraform' },

    // AI & Machine Learning
    { value: 'machine-learning', label: 'Machine Learning' },
    { value: 'deep-learning', label: 'Deep Learning' },
    { value: 'tensorflow', label: 'TensorFlow' },
    { value: 'pytorch', label: 'PyTorch' },
    { value: 'nlp', label: 'Natural Language Processing' },
    { value: 'computer-vision', label: 'Computer Vision' },

    // Mobile Development
    { value: 'react-native', label: 'React Native' },
    { value: 'flutter', label: 'Flutter' },
    { value: 'ios-development', label: 'iOS Development' },
    { value: 'android-development', label: 'Android Development' },

    // Others
    { value: 'agile', label: 'Agile Methodology' },
    { value: 'scrum', label: 'Scrum' },
    { value: 'api-development', label: 'API Development' },
    { value: 'microservices', label: 'Microservices Architecture' },
  ],

  healthcare: [
    { value: 'clinical-research', label: 'Clinical Research' },
    { value: 'medical-devices', label: 'Medical Devices' },
    { value: 'pharmaceutical', label: 'Pharmaceutical Knowledge' },
    { value: 'healthcare-regulations', label: 'Healthcare Regulations' },
    { value: 'patient-care', label: 'Patient Care' },
    { value: 'medical-imaging', label: 'Medical Imaging' },
    { value: 'telemedicine', label: 'Telemedicine' },
    { value: 'electronic-health-records', label: 'Electronic Health Records' },
    { value: 'medical-coding', label: 'Medical Coding' },
    { value: 'biostatistics', label: 'Biostatistics' },
    { value: 'nursing', label: 'Nursing' },
    { value: 'pharmacy', label: 'Pharmacy' },
    { value: 'laboratory-techniques', label: 'Laboratory Techniques' },
    { value: 'healthcare-administration', label: 'Healthcare Administration' },
  ],

  finance: [
    { value: 'financial-analysis', label: 'Financial Analysis' },
    { value: 'risk-management', label: 'Risk Management' },
    { value: 'investment-banking', label: 'Investment Banking' },
    { value: 'portfolio-management', label: 'Portfolio Management' },
    { value: 'accounting', label: 'Accounting' },
    { value: 'auditing', label: 'Auditing' },
    { value: 'tax-preparation', label: 'Tax Preparation' },
    { value: 'financial-planning', label: 'Financial Planning' },
    { value: 'insurance', label: 'Insurance' },
    { value: 'banking-operations', label: 'Banking Operations' },
    { value: 'credit-analysis', label: 'Credit Analysis' },
    { value: 'regulatory-compliance', label: 'Regulatory Compliance' },
    { value: 'fintech', label: 'Financial Technology' },
    { value: 'blockchain', label: 'Blockchain Technology' },
    { value: 'cryptocurrency', label: 'Cryptocurrency' },
  ],

  education: [
    { value: 'curriculum-development', label: 'Curriculum Development' },
    { value: 'instructional-design', label: 'Instructional Design' },
    { value: 'classroom-management', label: 'Classroom Management' },
    { value: 'educational-technology', label: 'Educational Technology' },
    { value: 'student-assessment', label: 'Student Assessment' },
    { value: 'online-learning', label: 'Online Learning' },
    { value: 'educational-research', label: 'Educational Research' },
    { value: 'special-education', label: 'Special Education' },
    { value: 'academic-administration', label: 'Academic Administration' },
    { value: 'tutoring', label: 'Tutoring' },
    { value: 'training-development', label: 'Training & Development' },
  ],

  manufacturing: [
    { value: 'quality-control', label: 'Quality Control' },
    { value: 'production-planning', label: 'Production Planning' },
    { value: 'lean-manufacturing', label: 'Lean Manufacturing' },
    { value: 'six-sigma', label: 'Six Sigma' },
    { value: 'supply-chain', label: 'Supply Chain Management' },
    { value: 'inventory-management', label: 'Inventory Management' },
    { value: 'automation', label: 'Industrial Automation' },
    { value: 'cad-design', label: 'CAD Design' },
    { value: 'process-improvement', label: 'Process Improvement' },
    { value: 'safety-compliance', label: 'Safety & Compliance' },
    { value: 'equipment-maintenance', label: 'Equipment Maintenance' },
  ],

  retail: [
    { value: 'customer-service', label: 'Customer Service' },
    { value: 'sales-management', label: 'Sales Management' },
    { value: 'merchandising', label: 'Merchandising' },
    { value: 'inventory-control', label: 'Inventory Control' },
    { value: 'pos-systems', label: 'POS Systems' },
    { value: 'e-commerce', label: 'E-commerce' },
    { value: 'retail-analytics', label: 'Retail Analytics' },
    { value: 'visual-merchandising', label: 'Visual Merchandising' },
    { value: 'buyer-relations', label: 'Buyer Relations' },
    { value: 'loss-prevention', label: 'Loss Prevention' },
  ],

  hospitality: [
    { value: 'hotel-management', label: 'Hotel Management' },
    { value: 'food-service', label: 'Food Service' },
    { value: 'event-planning', label: 'Event Planning' },
    { value: 'tourism', label: 'Tourism' },
    { value: 'restaurant-operations', label: 'Restaurant Operations' },
    { value: 'hospitality-service', label: 'Hospitality Service' },
    { value: 'catering', label: 'Catering' },
    { value: 'front-desk', label: 'Front Desk Operations' },
    { value: 'housekeeping', label: 'Housekeeping Management' },
    { value: 'revenue-management', label: 'Revenue Management' },
  ],

  consulting: [
    { value: 'strategy-consulting', label: 'Strategy Consulting' },
    { value: 'management-consulting', label: 'Management Consulting' },
    { value: 'it-consulting', label: 'IT Consulting' },
    { value: 'business-analysis', label: 'Business Analysis' },
    { value: 'process-improvement', label: 'Process Improvement' },
    { value: 'change-management', label: 'Change Management' },
    { value: 'project-management', label: 'Project Management' },
    { value: 'client-relations', label: 'Client Relations' },
    { value: 'presentation-skills', label: 'Presentation Skills' },
  ],

  media: [
    { value: 'content-creation', label: 'Content Creation' },
    { value: 'video-production', label: 'Video Production' },
    { value: 'graphic-design', label: 'Graphic Design' },
    { value: 'social-media', label: 'Social Media Management' },
    { value: 'journalism', label: 'Journalism' },
    { value: 'copywriting', label: 'Copywriting' },
    { value: 'broadcasting', label: 'Broadcasting' },
    { value: 'digital-marketing', label: 'Digital Marketing' },
    { value: 'seo', label: 'Search Engine Optimization' },
    { value: 'photography', label: 'Photography' },
  ],

  legal: [
    { value: 'legal-research', label: 'Legal Research' },
    { value: 'contract-law', label: 'Contract Law' },
    { value: 'litigation', label: 'Litigation' },
    { value: 'corporate-law', label: 'Corporate Law' },
    { value: 'intellectual-property', label: 'Intellectual Property' },
    { value: 'employment-law', label: 'Employment Law' },
    { value: 'regulatory-law', label: 'Regulatory Law' },
    { value: 'legal-writing', label: 'Legal Writing' },
    { value: 'paralegal', label: 'Paralegal Skills' },
  ],

  marketing: [
    { value: 'digital-marketing', label: 'Digital Marketing' },
    { value: 'content-marketing', label: 'Content Marketing' },
    { value: 'seo', label: 'Search Engine Optimization' },
    { value: 'sem', label: 'Search Engine Marketing' },
    { value: 'social-media-marketing', label: 'Social Media Marketing' },
    { value: 'email-marketing', label: 'Email Marketing' },
    { value: 'brand-management', label: 'Brand Management' },
    { value: 'market-research', label: 'Market Research' },
    { value: 'advertising', label: 'Advertising' },
    { value: 'public-relations', label: 'Public Relations' },
    { value: 'analytics', label: 'Marketing Analytics' },
    { value: 'crm', label: 'Customer Relationship Management' },
  ],

  // For other industries, we can include a general set of skills
  other: [
    { value: 'communication', label: 'Communication Skills' },
    { value: 'leadership', label: 'Leadership' },
    { value: 'problem-solving', label: 'Problem Solving' },
    { value: 'teamwork', label: 'Teamwork' },
    { value: 'time-management', label: 'Time Management' },
    { value: 'critical-thinking', label: 'Critical Thinking' },
    { value: 'adaptability', label: 'Adaptability' },
    { value: 'customer-service', label: 'Customer Service' },
    { value: 'project-management', label: 'Project Management' },
    { value: 'data-analysis', label: 'Data Analysis' },
    { value: 'microsoft-office', label: 'Microsoft Office' },
    { value: 'presentation-skills', label: 'Presentation Skills' },
  ],
};

// Helper functions
export function getIndustryIcon(industryValue: string) {
  const industry = industries.find((ind) => ind.value === industryValue);
  return industry?.icon || Building2;
}

export function getIndustryByValue(industryValue: string) {
  return industries.find((ind) => ind.value === industryValue);
}

export function getIndustryLabel(industryValue: string) {
  const industry = industries.find((ind) => ind.value === industryValue);
  return industry?.label || 'Unknown Industry';
}
