export interface Skill {
  value: string;
  label: string;
  category: SkillCategory;
}

export type SkillCategory =
  | 'programming'
  | 'frontend'
  | 'backend'
  | 'database'
  | 'devops'
  | 'mobile'
  | 'ai'
  | 'design'
  | 'testing'
  | 'healthcare'
  | 'finance'
  | 'marketing'
  | 'sales'
  | 'education'
  | 'legal'
  | 'manufacturing'
  | 'retail'
  | 'hospitality'
  | 'consulting'
  | 'management'
  | 'hr'
  | 'other';

export const allSkills: Skill[] = [
  // Programming Languages
  { value: 'javascript', label: 'JavaScript', category: 'programming' },
  { value: 'typescript', label: 'TypeScript', category: 'programming' },
  { value: 'python', label: 'Python', category: 'programming' },
  { value: 'java', label: 'Java', category: 'programming' },
  { value: 'csharp', label: 'C#', category: 'programming' },
  { value: 'cpp', label: 'C++', category: 'programming' },
  { value: 'go', label: 'Go', category: 'programming' },
  { value: 'rust', label: 'Rust', category: 'programming' },
  { value: 'php', label: 'PHP', category: 'programming' },
  { value: 'ruby', label: 'Ruby', category: 'programming' },
  { value: 'swift', label: 'Swift', category: 'programming' },
  { value: 'kotlin', label: 'Kotlin', category: 'programming' },

  // Frontend
  { value: 'react', label: 'React', category: 'frontend' },
  { value: 'angular', label: 'Angular', category: 'frontend' },
  { value: 'vue', label: 'Vue.js', category: 'frontend' },
  { value: 'nextjs', label: 'Next.js', category: 'frontend' },
  { value: 'html', label: 'HTML', category: 'frontend' },
  { value: 'css', label: 'CSS', category: 'frontend' },
  { value: 'sass', label: 'Sass/SCSS', category: 'frontend' },
  { value: 'tailwind', label: 'Tailwind CSS', category: 'frontend' },
  { value: 'bootstrap', label: 'Bootstrap', category: 'frontend' },
  { value: 'redux', label: 'Redux', category: 'frontend' },

  // Backend
  { value: 'nodejs', label: 'Node.js', category: 'backend' },
  { value: 'express', label: 'Express.js', category: 'backend' },
  { value: 'nestjs', label: 'NestJS', category: 'backend' },
  { value: 'django', label: 'Django', category: 'backend' },
  { value: 'flask', label: 'Flask', category: 'backend' },
  { value: 'fastapi', label: 'FastAPI', category: 'backend' },
  { value: 'spring', label: 'Spring Boot', category: 'backend' },
  { value: 'aspnet', label: 'ASP.NET Core', category: 'backend' },
  { value: 'laravel', label: 'Laravel', category: 'backend' },
  { value: 'rails', label: 'Ruby on Rails', category: 'backend' },
  { value: 'graphql', label: 'GraphQL', category: 'backend' },
  { value: 'rest', label: 'RESTful APIs', category: 'backend' },

  // Database
  { value: 'mongodb', label: 'MongoDB', category: 'database' },
  { value: 'mysql', label: 'MySQL', category: 'database' },
  { value: 'postgresql', label: 'PostgreSQL', category: 'database' },
  { value: 'sqlserver', label: 'SQL Server', category: 'database' },
  { value: 'redis', label: 'Redis', category: 'database' },
  { value: 'firebase', label: 'Firebase', category: 'database' },
  { value: 'prisma', label: 'Prisma', category: 'database' },

  // DevOps
  { value: 'docker', label: 'Docker', category: 'devops' },
  { value: 'kubernetes', label: 'Kubernetes', category: 'devops' },
  { value: 'aws', label: 'AWS', category: 'devops' },
  { value: 'azure', label: 'Azure', category: 'devops' },
  { value: 'gcp', label: 'Google Cloud', category: 'devops' },
  { value: 'jenkins', label: 'Jenkins', category: 'devops' },
  { value: 'github-actions', label: 'GitHub Actions', category: 'devops' },
  { value: 'terraform', label: 'Terraform', category: 'devops' },
  { value: 'linux', label: 'Linux', category: 'devops' },

  // Mobile
  { value: 'react-native', label: 'React Native', category: 'mobile' },
  { value: 'flutter', label: 'Flutter', category: 'mobile' },
  { value: 'ios', label: 'iOS Development', category: 'mobile' },
  { value: 'android', label: 'Android Development', category: 'mobile' },
  { value: 'xamarin', label: 'Xamarin', category: 'mobile' },

  // AI/ML
  { value: 'tensorflow', label: 'TensorFlow', category: 'ai' },
  { value: 'pytorch', label: 'PyTorch', category: 'ai' },
  { value: 'scikit-learn', label: 'scikit-learn', category: 'ai' },
  { value: 'nlp', label: 'Natural Language Processing', category: 'ai' },
  { value: 'computer-vision', label: 'Computer Vision', category: 'ai' },
  { value: 'data-science', label: 'Data Science', category: 'ai' },
  { value: 'machine-learning', label: 'Machine Learning', category: 'ai' },
  { value: 'deep-learning', label: 'Deep Learning', category: 'ai' },

  // Design
  { value: 'figma', label: 'Figma', category: 'design' },
  { value: 'sketch', label: 'Sketch', category: 'design' },
  { value: 'adobe-xd', label: 'Adobe XD', category: 'design' },
  { value: 'ui-design', label: 'UI Design', category: 'design' },
  { value: 'ux-design', label: 'UX Design', category: 'design' },
  { value: 'responsive-design', label: 'Responsive Design', category: 'design' },

  // Testing
  { value: 'jest', label: 'Jest', category: 'testing' },
  { value: 'cypress', label: 'Cypress', category: 'testing' },
  { value: 'selenium', label: 'Selenium', category: 'testing' },
  { value: 'testing-library', label: 'Testing Library', category: 'testing' },
  { value: 'junit', label: 'JUnit', category: 'testing' },

  // Healthcare
  { value: 'nursing', label: 'Nursing', category: 'healthcare' },
  { value: 'medicine', label: 'Medicine', category: 'healthcare' },
  { value: 'pharmacy', label: 'Pharmacy', category: 'healthcare' },
  { value: 'physical-therapy', label: 'Physical Therapy', category: 'healthcare' },
  { value: 'radiology', label: 'Radiology', category: 'healthcare' },
  { value: 'laboratory', label: 'Laboratory', category: 'healthcare' },
  { value: 'surgery', label: 'Surgery', category: 'healthcare' },
  { value: 'dentistry', label: 'Dentistry', category: 'healthcare' },
  { value: 'mental-health', label: 'Mental Health', category: 'healthcare' },
  {
    value: 'healthcare-administration',
    label: 'Healthcare Administration',
    category: 'healthcare',
  },
  { value: 'medical-research', label: 'Medical Research', category: 'healthcare' },
  { value: 'health-informatics', label: 'Health Informatics', category: 'healthcare' },

  // Finance
  { value: 'accounting', label: 'Accounting', category: 'finance' },
  { value: 'financial-analysis', label: 'Financial Analysis', category: 'finance' },
  { value: 'investment-banking', label: 'Investment Banking', category: 'finance' },
  { value: 'portfolio-management', label: 'Portfolio Management', category: 'finance' },
  { value: 'risk-management', label: 'Risk Management', category: 'finance' },
  { value: 'audit', label: 'Audit', category: 'finance' },
  { value: 'tax', label: 'Tax', category: 'finance' },
  { value: 'fintech', label: 'FinTech', category: 'finance' },
  { value: 'compliance', label: 'Compliance', category: 'finance' },
  { value: 'credit-analysis', label: 'Credit Analysis', category: 'finance' },
  { value: 'financial-planning', label: 'Financial Planning', category: 'finance' },

  // Marketing
  { value: 'digital-marketing', label: 'Digital Marketing', category: 'marketing' },
  { value: 'content-marketing', label: 'Content Marketing', category: 'marketing' },
  { value: 'seo', label: 'SEO', category: 'marketing' },
  { value: 'ppc', label: 'PPC Advertising', category: 'marketing' },
  { value: 'social-media', label: 'Social Media Marketing', category: 'marketing' },
  { value: 'email-marketing', label: 'Email Marketing', category: 'marketing' },
  { value: 'brand-management', label: 'Brand Management', category: 'marketing' },
  { value: 'market-research', label: 'Market Research', category: 'marketing' },
  { value: 'growth-hacking', label: 'Growth Hacking', category: 'marketing' },
  { value: 'copywriting', label: 'Copywriting', category: 'marketing' },
  { value: 'analytics', label: 'Marketing Analytics', category: 'marketing' },

  // Sales
  { value: 'b2b-sales', label: 'B2B Sales', category: 'sales' },
  { value: 'b2c-sales', label: 'B2C Sales', category: 'sales' },
  { value: 'inside-sales', label: 'Inside Sales', category: 'sales' },
  { value: 'field-sales', label: 'Field Sales', category: 'sales' },
  { value: 'account-management', label: 'Account Management', category: 'sales' },
  { value: 'lead-generation', label: 'Lead Generation', category: 'sales' },
  { value: 'sales-development', label: 'Sales Development', category: 'sales' },
  { value: 'crm', label: 'CRM', category: 'sales' },
  { value: 'negotiation', label: 'Negotiation', category: 'sales' },
  { value: 'cold-calling', label: 'Cold Calling', category: 'sales' },

  // Education
  { value: 'teaching', label: 'Teaching', category: 'education' },
  { value: 'curriculum-development', label: 'Curriculum Development', category: 'education' },
  { value: 'educational-technology', label: 'Educational Technology', category: 'education' },
  { value: 'training', label: 'Training', category: 'education' },
  { value: 'instructional-design', label: 'Instructional Design', category: 'education' },
  { value: 'academic-research', label: 'Academic Research', category: 'education' },
  { value: 'student-counseling', label: 'Student Counseling', category: 'education' },
  { value: 'administration', label: 'Educational Administration', category: 'education' },

  // Legal
  { value: 'corporate-law', label: 'Corporate Law', category: 'legal' },
  { value: 'litigation', label: 'Litigation', category: 'legal' },
  { value: 'contract-law', label: 'Contract Law', category: 'legal' },
  { value: 'intellectual-property', label: 'Intellectual Property', category: 'legal' },
  { value: 'employment-law', label: 'Employment Law', category: 'legal' },
  { value: 'regulatory', label: 'Regulatory', category: 'legal' },
  { value: 'legal-research', label: 'Legal Research', category: 'legal' },
  { value: 'paralegal', label: 'Paralegal', category: 'legal' },

  // Manufacturing
  { value: 'production', label: 'Production', category: 'manufacturing' },
  { value: 'quality-control', label: 'Quality Control', category: 'manufacturing' },
  { value: 'lean-manufacturing', label: 'Lean Manufacturing', category: 'manufacturing' },
  { value: 'supply-chain', label: 'Supply Chain', category: 'manufacturing' },
  { value: 'logistics', label: 'Logistics', category: 'manufacturing' },
  { value: 'operations', label: 'Operations', category: 'manufacturing' },
  { value: 'safety', label: 'Safety', category: 'manufacturing' },
  { value: 'maintenance', label: 'Maintenance', category: 'manufacturing' },

  // Retail
  { value: 'customer-service', label: 'Customer Service', category: 'retail' },
  { value: 'merchandising', label: 'Merchandising', category: 'retail' },
  { value: 'inventory-management', label: 'Inventory Management', category: 'retail' },
  { value: 'pos-systems', label: 'POS Systems', category: 'retail' },
  { value: 'visual-merchandising', label: 'Visual Merchandising', category: 'retail' },
  { value: 'store-management', label: 'Store Management', category: 'retail' },
  { value: 'e-commerce', label: 'E-commerce', category: 'retail' },

  // Hospitality
  { value: 'hotel-management', label: 'Hotel Management', category: 'hospitality' },
  { value: 'food-service', label: 'Food Service', category: 'hospitality' },
  { value: 'event-planning', label: 'Event Planning', category: 'hospitality' },
  { value: 'tourism', label: 'Tourism', category: 'hospitality' },
  { value: 'hospitality-service', label: 'Hospitality Service', category: 'hospitality' },
  { value: 'catering', label: 'Catering', category: 'hospitality' },

  // Consulting
  { value: 'strategy-consulting', label: 'Strategy Consulting', category: 'consulting' },
  { value: 'management-consulting', label: 'Management Consulting', category: 'consulting' },
  { value: 'it-consulting', label: 'IT Consulting', category: 'consulting' },
  { value: 'business-analysis', label: 'Business Analysis', category: 'consulting' },
  { value: 'process-improvement', label: 'Process Improvement', category: 'consulting' },
  { value: 'change-management', label: 'Change Management', category: 'consulting' },

  // Management
  { value: 'project-management', label: 'Project Management', category: 'management' },
  { value: 'team-leadership', label: 'Team Leadership', category: 'management' },
  { value: 'strategic-planning', label: 'Strategic Planning', category: 'management' },
  { value: 'budget-management', label: 'Budget Management', category: 'management' },
  { value: 'performance-management', label: 'Performance Management', category: 'management' },
  { value: 'stakeholder-management', label: 'Stakeholder Management', category: 'management' },

  // HR
  { value: 'recruitment', label: 'Recruitment', category: 'hr' },
  { value: 'talent-acquisition', label: 'Talent Acquisition', category: 'hr' },
  { value: 'employee-relations', label: 'Employee Relations', category: 'hr' },
  { value: 'compensation', label: 'Compensation & Benefits', category: 'hr' },
  { value: 'hr-analytics', label: 'HR Analytics', category: 'hr' },
  { value: 'learning-development', label: 'Learning & Development', category: 'hr' },
  { value: 'organizational-development', label: 'Organizational Development', category: 'hr' },

  // Other
  { value: 'communication', label: 'Communication', category: 'other' },
  { value: 'problem-solving', label: 'Problem Solving', category: 'other' },
  { value: 'critical-thinking', label: 'Critical Thinking', category: 'other' },
  { value: 'leadership', label: 'Leadership', category: 'other' },
  { value: 'time-management', label: 'Time Management', category: 'other' },
  { value: 'adaptability', label: 'Adaptability', category: 'other' },
  { value: 'teamwork', label: 'Teamwork', category: 'other' },
  { value: 'creativity', label: 'Creativity', category: 'other' },
];

// Group skills by category for easier selection
export const skillsByCategory = allSkills.reduce<Record<SkillCategory, Skill[]>>(
  (acc, skill) => {
    if (!acc[skill.category]) {
      acc[skill.category] = [];
    }
    acc[skill.category].push(skill);
    return acc;
  },
  {
    programming: [],
    frontend: [],
    backend: [],
    database: [],
    devops: [],
    mobile: [],
    ai: [],
    design: [],
    testing: [],
    healthcare: [],
    finance: [],
    marketing: [],
    sales: [],
    education: [],
    legal: [],
    manufacturing: [],
    retail: [],
    hospitality: [],
    consulting: [],
    management: [],
    hr: [],
    other: [],
  }
);

// Get all skill values as a simple array
export const skillValues = allSkills.map((skill) => skill.value);

// Get all skill labels as a simple array
export const skillLabels = allSkills.map((skill) => skill.label);

// Function to get label from value
export const getSkillLabel = (value: string): string => {
  const skill = allSkills.find((s) => s.value === value);
  return skill ? skill.label : value;
};

// Function to get skill object from value
export const getSkillByValue = (value: string): Skill | undefined => {
  return allSkills.find((s) => s.value === value);
};

// Category labels for display
export const categoryLabels: Record<SkillCategory, string> = {
  programming: 'Programming Languages',
  frontend: 'Frontend Development',
  backend: 'Backend Development',
  database: 'Database & Storage',
  devops: 'DevOps & Cloud',
  mobile: 'Mobile Development',
  ai: 'AI & Machine Learning',
  design: 'Design & UX',
  testing: 'Testing & QA',
  healthcare: 'Healthcare & Medical',
  finance: 'Finance & Accounting',
  marketing: 'Marketing & Advertising',
  sales: 'Sales & Business Development',
  education: 'Education & Training',
  legal: 'Legal & Compliance',
  manufacturing: 'Manufacturing & Production',
  retail: 'Retail & E-commerce',
  hospitality: 'Hospitality & Tourism',
  consulting: 'Consulting & Advisory',
  management: 'Management & Leadership',
  hr: 'Human Resources',
  other: 'Soft Skills & Other',
};
