import type { Document } from 'mongoose';
import { z } from 'zod';

import { IOrganization } from './organization';
import { IUser } from './user';

// Database interface types
export interface IMember extends Document {
  _doc: IMember;
  _id: string;
  userId: string | IUser;
  organizationId: string | IOrganization;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

export type OrganizationMemberSortField = 'name' | 'email' | 'createdAt';

export const addMemberFormSchema = z.object({
  userId: z.string().min(1, 'User ID is required'),
  organizationId: z.string().min(1, 'Organization ID is required'),
  roles: z.array(z.enum(['admin', 'member', 'owner'])).min(1, 'At least one role is required'),
});
