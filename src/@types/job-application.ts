import type { Document } from 'mongoose';

// Database interface types

// Represents the state of an interview process
export interface InterviewState {
  currentPhase: string; // Based on InterviewPhase enum
  difficultyLevel: 'easy' | 'normal' | 'hard' | 'expert' | 'advanced'; // Interview difficulty level
  technicalQuestionsAsked: number; // Track number of technical questions asked (out of 3)
  projectQuestionsAsked: number; // Track number of project questions asked (out of 3)
  behavioralQuestionsAsked: number; // Track number of behavioral questions asked (out of 3)
  lastQuestion?: string; // The last question asked
  askedQuestions: {
    id: string;
    question: string;
    category: string;
  }[];
  feedback: {
    technicalSkills?: number; // 1-5 rating
    communicationSkills?: number; // 1-5 rating
    problemSolving?: number; // 1-5 rating
    cultureFit?: number; // 1-5 rating
    overallImpression?: string;
    strengths?: string[];
    areasOfImprovement?: string[];
  };
  completedAt?: Date;
  // Timer-related fields
  timerStartedAt?: Date; // When the interview timer started
  timerDurationMinutes?: number; // Interview duration in minutes
  isTimerExpired?: boolean; // Whether the timer has expired
  interruptedAt?: Date; // When the interview was interrupted (if applicable)
  interruptionReason?: 'timer_expired' | 'technical_issue' | 'user_action'; // Reason for interruption
}

export interface ParsedResume {
  extractedText: string;
  about: string;
  skills: string[];
  experience: {
    years: number;
    companies: string[];
  };
  education: {
    degree: string;
    institution: string;
  }[];
  analyzedAt: Date;
  // Match-specific fields
  matchScore?: number; // 0-100 score indicating compatibility with job
  aiComments?: string; // AI-generated analysis of the match
  matchedAt?: Date; // When the match was performed

  // For future expansion
  topSkillMatches?: string[]; // Skills that matched job requirements
  missingSkills?: string[]; // Important skills from job that candidate lacks
}

export interface MonitoringImage {
  s3Key: string;
  timestamp: Date;
}

// Interface for interview chat messages
export interface InterviewMessage {
  text: string;
  sender: 'ai' | 'user' | 'system';
  timestamp: Date;
  questionId?: string; // Optional field to link messages to specific questions
  questionCategory?: string; // Technical, project, or behavioral
  feedback?: string; // AI feedback on a user's answer
  audioS3Key?: string; // S3 key for the audio file of AI response
  audioS3Bucket?: string; // S3 bucket containing the audio file
  audioUrl?: string; // Direct URL to the audio file for immediate playback
}

export interface IJobApplication extends Document {
  jobId: string;
  userId: string; // User ID reference
  candidateName?: string; // Optional
  email?: string; // Optional
  resumeUrl: string;
  resumeBase64: string;
  fileName: string;
  s3Key?: string; // S3 object key for generating signed URLs
  s3Bucket?: string; // S3 bucket name for generating signed URLs
  preferredLanguage: string;
  status: 'pending' | 'reviewed' | 'accepted' | 'rejected';
  parsedResume?: ParsedResume; // Parsed resume data including match score and AI comments
  interviewState?: InterviewState; // Track the state of the interview process
  monitoringEnabled?: boolean; // Whether camera monitoring is enabled
  monitoringInterval?: number; // Interval in milliseconds between captures
  monitoringImages?: MonitoringImage[]; // Array of captured monitoring images
  interviewChatHistory?: InterviewMessage[]; // Chat history from interview sessions
  createdAt: Date;
  updatedAt: Date;
}
