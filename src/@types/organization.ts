import type { Document } from 'mongoose';
import { z } from 'zod';

import type { PaginationMeta } from './common';

// Database interface types
export interface IOrganization extends Document {
  _doc: IOrganization;
  id: string;
  name: string;
  slug: string;
  description?: string;
  logo: string;
  metadata?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Type alias for Organization
export type Organization = IOrganization;

export interface PaginatedOrganizations {
  data: Organization[];
  pagination: PaginationMeta;
}

// Filter and sort types
export interface OrganizationFilters {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: 'name' | 'slug' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
  organizationId?: string; // Optional filter for organization ID
  checkAdmin?: boolean; // Optional filter for admin users
}

export type OrganizationSortField = 'name' | 'slug' | 'createdAt' | 'updatedAt';

// Organization creation schema
export const createOrganizationSchema = z.object({
  name: z.string().min(2).max(100),
  slug: z
    .string()
    .min(2)
    .max(50)
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      'Slug must be lowercase and can only contain letters, numbers, and hyphens'
    ),
  description: z.string().optional(),
  logo: z.string().url().optional(),
  metadata: z.string().optional(),
});

// Get all organizations schema
export const getAllOrganizationsSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'slug', 'createdAt', 'updatedAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  organizationId: z.string().min(24).max(24).optional(), // Optional filter for organization ID
  checkAdmin: z.boolean().default(false), // Optional filter for admin users
});

// Get organization by slug schema
export const getOrganizationBySlugSchema = z.string().min(2).max(50);

// Get organization by ID schema
export const getOrganizationByIdSchema = z.string().min(24).max(24);

// Update organization schema
export const updateOrganizationSchema = z.object({
  id: z.string().min(24).max(24),
  name: z.string().min(2).max(100).optional(),
  slug: z
    .string()
    .min(2)
    .max(50)
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      'Slug must be lowercase and can only contain letters, numbers, and hyphens'
    )
    .optional(),
  description: z.string().optional(),
  logo: z.string().url().optional(),
  metadata: z.string().optional(),
});

// Get organization members schema
export const getOrganizationMembersSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'email', 'createdAt', 'updatedAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  organizationId: z.string().min(24).max(24).optional(), // Optional filter for organization ID
});

// Get organization jobs schema
export const getOrganizationJobsSchema = z.string().min(24).max(24);

// Form validation schema for adding/editing organizations
export const addOrganizationSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  slug: z
    .string()
    .min(2, 'Slug must be at least 2 characters')
    .max(50, 'Slug must be less than 50 characters')
    .regex(
      /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
      'Slug must be lowercase and can only contain letters, numbers, and hyphens'
    ),
  description: z.string().optional(),
  logo: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  metadata: z.string().optional(),
});

// Type exports for TypeScript inference
export type CreateOrganizationInput = z.infer<typeof createOrganizationSchema>;
export type GetAllOrganizationsInput = z.infer<typeof getAllOrganizationsSchema>;
export type GetOrganizationBySlugInput = z.infer<typeof getOrganizationBySlugSchema>;
export type GetOrganizationByIdInput = z.infer<typeof getOrganizationByIdSchema>;
export type UpdateOrganizationInput = z.infer<typeof updateOrganizationSchema>;
export type GetOrganizationMembersInput = z.infer<typeof getOrganizationMembersSchema>;
export type GetOrganizationJobsInput = z.infer<typeof getOrganizationJobsSchema>;
export type AddOrganizationFormData = z.infer<typeof addOrganizationSchema>;
