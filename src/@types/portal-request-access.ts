import type { Document } from 'mongoose';
import { z } from 'zod';

import { PaginationMeta } from './common';

export interface IPortalRequestAccess extends Document {
  _doc: IPortalRequestAccess;
  id: string;
  full_name: string;
  work_email: string;
  job_title: string;
  phone_number: string;
  company_name: string;
  company_size: string;
  industry: string;
  monthly_hires: string;
  hiring_challenge: string;
  referral_source?: string;
  status?: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
  updatedAt: Date;
}

export type PortalRequestAccess = IPortalRequestAccess;

export interface PaginatedPortalRequests {
  data: PortalRequestAccess[];
  pagination: PaginationMeta;
}

export interface PortalRequestFilters {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export type PortalRequestSortField =
  | 'createdAt'
  | 'updatedAt'
  | 'full_name'
  | 'work_email'
  | 'company_name'
  | 'company_size'
  | 'industry'
  | 'status';

export const requestAccessSchema = z.object({
  full_name: z.string().min(2, 'Full name must be at least 2 characters'),
  work_email: z.string().email('Please enter a valid work email address'),
  job_title: z.string().min(2, 'Job title is required'),
  phone_number: z.string().min(10, 'Please enter a valid phone number'),
  company_name: z.string().min(2, 'Company name is required'),
  company_size: z.string().min(1, 'Please select company size'),
  industry: z.string().min(1, 'Please select your industry'),
  monthly_hires: z.string().min(1, 'Please select estimated monthly hires'),
  hiring_challenge: z.string().min(20, 'Please provide more details (minimum 20 characters)'),
  referral_source: z.string().optional(),
});

export const getAllPortalRequestsSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  sortBy: z
    .enum([
      'createdAt',
      'updatedAt',
      'full_name',
      'work_email',
      'company_name',
      'company_size',
      'industry',
      'status',
    ])
    .default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Get Portal Request by ID schema
export const getPortalRequestByIdSchema = z.string().min(24).max(24);
