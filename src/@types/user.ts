import type { Document } from 'mongoose';

// Database interface types
export interface IUser extends Document {
  _doc: IUser;
  id: string;
  name: string;
  role: 'user' | 'recruiter' | 'admin';
  email: string;
  emailVerified: boolean;
  image: string;
  createdAt: Date;
  updatedAt: Date;
}

// Type aliases for better semantic understanding
export type ICandidate = IUser;
export type IRecruiter = IUser;
