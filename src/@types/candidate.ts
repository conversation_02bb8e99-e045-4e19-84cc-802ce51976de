import { z } from 'zod';

import type { PaginationMeta } from './common';

// Database interface types
export interface Candidate {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image: string;
  role: 'user' | 'admin' | 'recruiter';
  createdAt: Date;
  updatedAt: Date;
}

export interface PaginatedCandidates {
  data: Candidate[];
  pagination: PaginationMeta;
}

export interface CandidateFilters {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: 'name' | 'email' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

export type CandidateSortField = 'name' | 'email' | 'createdAt' | 'updatedAt';

// Candidate creation schema
export const createCandidateSchema = z.object({
  name: z.string().min(2).max(100),
  email: z.string().email(),
  emailVerified: z.boolean().optional().default(false),
  image: z.string().url().optional(),
});

// Get all candidates schema
export const getAllCandidatesSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  sortBy: z.enum(['name', 'email', 'createdAt', 'updatedAt']).default('name'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Get candidate by ID schema
export const getCandidateByIdSchema = z.string().min(24).max(24);

// Get candidate by email schema
export const getCandidateByEmailSchema = z.string().email();

// Update candidate schema
export const updateCandidateSchema = z.object({
  id: z.string().min(24).max(24),
  name: z.string().min(2).max(100).optional(),
  email: z.string().email().optional(),
  emailVerified: z.boolean().optional(),
  image: z.string().url().optional(),
});

// Delete candidate schema
export const deleteCandidateSchema = z.string().min(24).max(24);

// Form validation schema for adding/editing candidates
export const addCandidateSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Please enter a valid email address'),
  emailVerified: z.boolean(),
});

// Type exports for TypeScript inference
export type CreateCandidateInput = z.infer<typeof createCandidateSchema>;
export type GetAllCandidatesInput = z.infer<typeof getAllCandidatesSchema>;
export type GetCandidateByIdInput = z.infer<typeof getCandidateByIdSchema>;
export type GetCandidateByEmailInput = z.infer<typeof getCandidateByEmailSchema>;
export type UpdateCandidateInput = z.infer<typeof updateCandidateSchema>;
export type DeleteCandidateInput = z.infer<typeof deleteCandidateSchema>;
export type AddCandidateFormData = z.infer<typeof addCandidateSchema>;
