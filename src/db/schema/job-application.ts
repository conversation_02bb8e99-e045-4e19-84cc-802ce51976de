import mongoose, { Schema } from 'mongoose';

import type { IJobApplication } from '@/@types/job-application';

import { InterviewStateSchema } from './interview-state';

const JobApplicationSchema = new Schema(
  {
    jobId: {
      type: Schema.Types.ObjectId,
      ref: 'Job',
      required: [true, 'Job ID is required'],
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User ID is required'],
    },
    candidateName: String,
    email: String,
    resumeUrl: {
      type: String,
      required: [true, 'Resume URL is required'],
    },
    resumeBase64: {
      type: String,
      required: [true, 'Resume base64 content is required'],
    },
    fileName: {
      type: String,
      required: [true, 'File name is required'],
    },
    s3Key: String,
    s3Bucket: String,
    preferredLanguage: {
      type: String,
      required: [true, 'Preferred language is required'],
    },
    status: {
      type: String,
      enum: ['pending', 'reviewed', 'accepted', 'rejected'],
      default: 'pending',
    },
    parsedResume: {
      extractedText: String,
      about: String,
      skills: [String],
      experience: {
        years: Number,
        companies: [String],
      },
      education: [
        {
          degree: String,
          institution: String,
        },
      ],
      analyzedAt: Date,
      matchScore: Number,
      aiComments: String,
      matchedAt: Date,
      topSkillMatches: [String],
      missingSkills: [String],
    },
    monitoringEnabled: {
      type: Boolean,
      default: false,
    },
    monitoringInterval: {
      type: Number,
      default: 30000, // Default 30 seconds
    },
    monitoringImages: [
      {
        s3Key: String,
        timestamp: Date,
      },
    ],
    interviewChatHistory: [
      {
        text: String,
        sender: {
          type: String,
          enum: ['ai', 'user', 'system'],
        },
        timestamp: Date,
        questionId: String,
        questionCategory: String,
        feedback: String,
        audioS3Key: String, // S3 key for the audio file of AI response
        audioS3Bucket: String, // S3 bucket containing the audio file
      },
    ],
    interviewState: InterviewStateSchema,
  },
  {
    timestamps: true,
    collection: 'jobapplication',
  }
);

// Prevent duplicate exports with NextJS hot reloading
export const JobApplication =
  mongoose.models.JobApplication ||
  mongoose.model<IJobApplication>('JobApplication', JobApplicationSchema);

export default JobApplication;
