import type { Model } from 'mongoose';
import mongoose, { Schema } from 'mongoose';

import type { IInvitation } from '@/@types/invitation';

const InvitationSchema: Schema = new Schema<IInvitation>(
  {
    email: { type: String, required: true },
    inviterId: { type: String, required: true },
    organizationId: { type: String, required: true },
    role: { type: String, required: true },
    status: {
      type: String,
      required: true,
      enum: ['pending', 'accepted', 'expired', 'cancelled'],
      default: 'pending',
    },
    expiresAt: { type: Date, required: true },
  },
  {
    timestamps: true,
    collection: 'invitation',
  }
);

// Create compound index to prevent duplicate invitations
InvitationSchema.index({ email: 1, organizationId: 1, status: 1 }, { unique: true });

// Create indexes for efficient queries
InvitationSchema.index({ organizationId: 1 });
InvitationSchema.index({ inviterId: 1 });
InvitationSchema.index({ email: 1 });
InvitationSchema.index({ status: 1 });
InvitationSchema.index({ expiresAt: 1 });

const Invitation: Model<IInvitation> =
  mongoose.models.Invitation || mongoose.model<IInvitation>('Invitation', InvitationSchema);

export default Invitation;
