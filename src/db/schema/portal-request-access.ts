import type { Model } from 'mongoose';
import mongoose, { Schema } from 'mongoose';

import { IPortalRequestAccess } from '@/@types/portal-request-access';

const PortalRequestAccessSchema = new Schema<IPortalRequestAccess>(
  {
    full_name: { type: String, required: true },
    work_email: { type: String, required: true },
    job_title: { type: String, required: true },
    phone_number: { type: String, required: true },
    company_name: { type: String, required: true },
    company_size: { type: String, required: true },
    industry: { type: String, required: true },
    monthly_hires: { type: String, required: true },
    hiring_challenge: { type: String, required: true },
    referral_source: { type: String, default: '' },
    status: { type: String, enum: ['pending', 'approved', 'rejected'], default: 'pending' },
  },
  {
    timestamps: true,
    collection: 'portal_request_access',
    toJSON: {
      transform: function (doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

export const PortalRequestAccess: Model<IPortalRequestAccess> =
  mongoose.models.PortalRequestAccess ||
  (typeof window === 'undefined' && typeof global !== 'undefined' && !('EdgeRuntime' in global)
    ? mongoose.model<IPortalRequestAccess>('PortalRequestAccess', PortalRequestAccessSchema)
    : (null as unknown as Model<IPortalRequestAccess>));
