import type { Model } from 'mongoose';
import mongoose, { Schema } from 'mongoose';

import type { IOrganization } from '@/@types/organization';

const OrganizationSchema = new Schema<IOrganization>(
  {
    name: { type: String, required: true },
    slug: { type: String, required: true, unique: true },
    description: { type: String },
    metadata: { type: String, default: '' }, // Optional metadata field
    logo: { type: String, required: true },
  },
  {
    timestamps: true,
    collection: 'organization',
    toJSON: {
      transform: function (doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);
const Organization: Model<IOrganization> =
  mongoose.models.Organization || mongoose.model<IOrganization>('Organization', OrganizationSchema);

export default Organization;
