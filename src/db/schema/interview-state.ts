import { Schema } from 'mongoose';

// Define the schema for interview state
export const InterviewStateSchema = new Schema(
  {
    currentPhase: {
      type: String,
      enum: [
        'introduction',
        'candidate_introduction',
        'technical_questions',
        'project_discussion',
        'behavioral_questions',
        'conclusion',
        'completed',
        'interrupted',
      ],
      default: 'introduction',
    },
    difficultyLevel: {
      type: String,
      enum: ['easy', 'normal', 'hard', 'expert', 'advanced'],
      default: 'normal',
    },
    technicalQuestionsAsked: {
      type: Number,
      default: 0,
    },
    projectQuestionsAsked: {
      type: Number,
      default: 0,
    },
    behavioralQuestionsAsked: {
      type: Number,
      default: 0,
    },
    lastQuestion: {
      type: String,
    },
    askedQuestions: [
      {
        id: String,
        question: String,
        category: String,
      },
    ],
    feedback: {
      technicalSkills: Number,
      communicationSkills: Number,
      problemSolving: Number,
      cultureFit: Number,
      overallImpression: String,
      strengths: [String],
      areasOfImprovement: [String],
    },
    completedAt: Date,
    // Timer-related fields
    timerStartedAt: Date,
    timerDurationMinutes: Number,
    isTimerExpired: {
      type: Boolean,
      default: false,
    },
    interruptedAt: Date,
    interruptionReason: {
      type: String,
      enum: ['timer_expired', 'technical_issue', 'user_action'],
    },
  },
  { _id: false }
);
