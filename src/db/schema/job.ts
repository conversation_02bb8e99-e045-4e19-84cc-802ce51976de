import type { Model } from 'mongoose';
import mongoose, { Schema } from 'mongoose';

import type { IJob } from '@/@types/job';

// Define the schema
const JobSchema = new Schema<IJob>(
  {
    title: {
      type: String,
      required: [true, 'Please provide a job title'],
      maxlength: [100, 'Title cannot be more than 100 characters'],
    },
    description: {
      type: String,
      required: [true, 'Please provide a job description'],
    },
    organizationId: {
      type: Schema.Types.ObjectId,
      ref: 'Organization',
      required: [true, 'Please provide an organization ID'],
    },
    organizationName: {
      type: String,
      maxlength: [100, 'Organization name cannot be more than 100 characters'],
    },
    industry: {
      type: String,
      required: [true, 'Please provide an industry'],
    },
    expiryDate: {
      type: Date,
      required: [true, 'Please provide an expiry date'],
    },
    location: {
      type: String,
      required: [true, 'Please provide a location'],
    },
    salary: {
      type: String,
    },
    currency: {
      type: String,
    },
    skills: {
      type: [String],
      required: [true, 'Please provide at least one skill'],
    },
    requirements: {
      type: String,
    },
    benefits: {
      type: String,
    },
    status: {
      type: String,
      enum: ['draft', 'published', 'expired', 'deleted'],
      default: 'draft',
    },
    recruiter: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    interviewConfig: {
      duration: {
        type: Number,
        required: [true, 'Interview duration is required'],
        min: [5, 'Interview duration must be at least 5 minutes'],
        max: [120, 'Interview duration cannot exceed 120 minutes'],
      },
      instructions: {
        type: String,
      },
      difficultyLevel: {
        type: String,
        enum: ['easy', 'normal', 'hard', 'expert', 'advanced'],
        default: 'normal',
      },
      screenMonitoring: {
        type: Boolean,
        default: false,
      },
      screenMonitoringMode: {
        type: String,
        enum: ['photo', 'video'],
        default: 'photo',
      },
      screenMonitoringInterval: {
        type: Number,
        enum: [30, 60],
        default: 30,
      },
      cameraMonitoring: {
        type: Boolean,
        default: false,
      },
      cameraMonitoringMode: {
        type: String,
        enum: ['photo', 'video'],
        default: 'photo',
      },
      cameraMonitoringInterval: {
        type: Number,
        enum: [30, 60],
        default: 30,
      },
    },
    questionsConfig: {
      mode: {
        type: String,
        enum: ['manual', 'ai-mode'],
        required: true,
      },
      totalQuestions: {
        type: Number,
        required: true,
        min: 1,
        max: 50,
      },
      categoryConfigs: {
        type: [
          {
            type: { type: String, required: true },
            numberOfQuestions: { type: Number, required: true, min: 1, max: 20 },
          },
        ],
        required: true,
      },
      questionTypes: {
        type: [String],
        required: true,
      },
      questions: {
        type: [
          {
            id: { type: String, required: true },
            type: { type: String, required: true },
            question: { type: String, required: true },
            isAIGenerated: { type: Boolean, default: false },
          },
        ],
        required: false, // Only required for manual mode, optional for ai-mode
      },
    },
    deletedAt: {
      type: Date,
      required: false,
    },
    deletedBy: {
      type: String,
      required: false,
    },
  },
  {
    timestamps: true,
    collection: 'job',
    toJSON: {
      transform: function (doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
    toObject: {
      transform: function (doc, ret) {
        ret.id = ret._id;
        delete ret._id;
        delete ret.__v;
        return ret;
      },
    },
  }
);

// Only define the model in a Node.js environment, not in Edge runtime
const Job: Model<IJob> =
  mongoose.models.Job ||
  (typeof window === 'undefined' && typeof global !== 'undefined' && !('EdgeRuntime' in global)
    ? mongoose.model<IJob>('Job', JobSchema)
    : (null as unknown as Model<IJob>));

export default Job;
