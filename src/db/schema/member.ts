import type { Model } from 'mongoose';
import mongoose, { Schema } from 'mongoose';

import type { IMember } from '@/@types/member';

const MemberSchema: Schema = new Schema<IMember>(
  {
    userId: { type: Schema.Types.ObjectId, required: true, ref: 'User' },
    organizationId: { type: Schema.Types.ObjectId, required: true, ref: 'Organization' },
    role: { type: String, required: true },
  },
  {
    timestamps: true,
    collection: 'member',
  }
);

// Create compound index to ensure a user can only be a member of an organization once
MemberSchema.index({ userId: 1, organizationId: 1 }, { unique: true });

// Create indexes for efficient queries
MemberSchema.index({ organizationId: 1 });
MemberSchema.index({ userId: 1 });

const Member: Model<IMember> =
  mongoose.models.Member || mongoose.model<IMember>('Member', MemberSchema);

export default Member;
