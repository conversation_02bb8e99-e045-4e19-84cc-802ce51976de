import { TRPCError } from '@trpc/server';

/**
 * Helper functions to interact with Better Auth's organization system
 */
export class BetterAuthOrgHelper {
  private static getAuthHeaders(headers: Headers) {
    return {
      'Content-Type': 'application/json',
      Authorization: headers.get('Authorization') || '',
      Cookie: headers.get('<PERSON>ie') || '',
    };
  }

  /**
   * Send invitation to a user to join an organization
   */
  static async inviteMember(params: {
    email: string;
    organizationId: string;
    role: string;
    headers: Headers;
  }) {
    try {
      const response = await fetch(
        `${process.env.BETTER_AUTH_URL || 'http://localhost:3000'}/api/auth/organization/invite-member`,
        {
          method: 'POST',
          headers: this.getAuthHeaders(params.headers),
          body: JSON.stringify({
            email: params.email,
            organizationId: params.organizationId,
            role: params.role,
          }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to send invitation');
      }

      return await response.json();
    } catch (error) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: error instanceof Error ? error.message : 'Failed to send invitation',
      });
    }
  }

  /**
   * Accept an invitation
   */
  static async acceptInvitation(params: { invitationId: string; headers: Headers }) {
    try {
      const response = await fetch(
        `${process.env.BETTER_AUTH_URL || 'http://localhost:3000'}/api/auth/organization/accept-invitation`,
        {
          method: 'POST',
          headers: this.getAuthHeaders(params.headers),
          body: JSON.stringify({ invitationId: params.invitationId }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to accept invitation');
      }

      return await response.json();
    } catch (error) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: error instanceof Error ? error.message : 'Failed to accept invitation',
      });
    }
  }

  /**
   * Reject an invitation
   */
  static async rejectInvitation(params: { invitationId: string; headers: Headers }) {
    try {
      const response = await fetch(
        `${process.env.BETTER_AUTH_URL || 'http://localhost:3000'}/api/auth/organization/reject-invitation`,
        {
          method: 'POST',
          headers: this.getAuthHeaders(params.headers),
          body: JSON.stringify({ invitationId: params.invitationId }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to reject invitation');
      }

      return await response.json();
    } catch (error) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: error instanceof Error ? error.message : 'Failed to reject invitation',
      });
    }
  }

  /**
   * Cancel an invitation
   */
  static async cancelInvitation(params: { invitationId: string; headers: Headers }) {
    try {
      const response = await fetch(
        `${process.env.BETTER_AUTH_URL || 'http://localhost:3000'}/api/auth/organization/cancel-invitation`,
        {
          method: 'POST',
          headers: this.getAuthHeaders(params.headers),
          body: JSON.stringify({ invitationId: params.invitationId }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to cancel invitation');
      }

      return await response.json();
    } catch (error) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: error instanceof Error ? error.message : 'Failed to cancel invitation',
      });
    }
  }

  /**
   * Update member role
   */
  static async updateMemberRole(params: {
    memberId: string;
    role: 'admin' | 'member' | 'owner';
    headers: Headers;
  }) {
    try {
      const response = await fetch(
        `${process.env.BETTER_AUTH_URL || 'http://localhost:3000'}/api/auth/organization/update-member-role`,
        {
          method: 'POST',
          headers: this.getAuthHeaders(params.headers),
          body: JSON.stringify({ memberId: params.memberId, role: params.role }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update member role');
      }

      return await response.json();
    } catch (error) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: error instanceof Error ? error.message : 'Failed to update member role',
      });
    }
  }

  /**
   * Remove member from organization
   */
  static async removeMember(params: { memberId: string; headers: Headers }) {
    try {
      const response = await fetch(
        `${process.env.BETTER_AUTH_URL || 'http://localhost:3000'}/api/auth/organization/remove-member`,
        {
          method: 'POST',
          headers: this.getAuthHeaders(params.headers),
          body: JSON.stringify({ memberId: params.memberId }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to remove member');
      }

      return await response.json();
    } catch (error) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: error instanceof Error ? error.message : 'Failed to remove member',
      });
    }
  }

  /**
   * Leave organization
   */
  static async leaveOrganization(params: { organizationId: string; headers: Headers }) {
    try {
      const response = await fetch(
        `${process.env.BETTER_AUTH_URL || 'http://localhost:3000'}/api/auth/organization/leave-organization`,
        {
          method: 'POST',
          headers: this.getAuthHeaders(params.headers),
          body: JSON.stringify({ organizationId: params.organizationId }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to leave organization');
      }

      return await response.json();
    } catch (error) {
      throw new TRPCError({
        code: 'BAD_REQUEST',
        message: error instanceof Error ? error.message : 'Failed to leave organization',
      });
    }
  }

  /**
   * Get organization members
   */
  static async getMembers(params: { organizationId: string }) {
    try {
      const response = await fetch(
        `${process.env.BETTER_AUTH_URL || 'http://localhost:3000'}/api/auth/organization/get-members?organizationId=${params.organizationId}`,
        {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to get members');
      }

      return await response.json();
    } catch (error) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: error instanceof Error ? error.message : 'Failed to get members',
      });
    }
  }

  /**
   * List invitations for organization
   */
  static async listInvitations(params: { organizationId: string }) {
    try {
      const response = await fetch(
        `${process.env.BETTER_AUTH_URL || 'http://localhost:3000'}/api/auth/organization/get-invitations?organizationId=${params.organizationId}`,
        {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to get invitations');
      }

      return await response.json();
    } catch (error) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: error instanceof Error ? error.message : 'Failed to get invitations',
      });
    }
  }

  /**
   * Get active member (current user's membership)
   */
  static async getActiveMember(params: { organizationId: string }) {
    try {
      const response = await fetch(
        `${process.env.BETTER_AUTH_URL || 'http://localhost:3000'}/api/auth/organization/get-active-member?organizationId=${params.organizationId}`,
        {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to get active member');
      }

      return await response.json();
    } catch (error) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: error instanceof Error ? error.message : 'Failed to get active member',
      });
    }
  }
}
