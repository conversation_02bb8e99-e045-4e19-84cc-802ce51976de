import { createCallerFactory, createTRPCRouter } from '@/server/api/trpc';

import { adminAuthRouter } from './routers/admin-auth';
import { authRouter } from './routers/auth';
import { candidateRouter } from './routers/candidate';
import { dashboardRouter } from './routers/dashboard';
import { jobRouter } from './routers/job';
import { memberRouter } from './routers/member';
import { organizationRouter } from './routers/organization';
import { portalRequestRouter } from './routers/portal-request';
import { recruiterRouter } from './routers/recruiter';

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  auth: authRouter,
  adminAuth: adminAuthRouter,
  organization: organizationRouter,
  candidate: candidateRouter,
  recruiter: recruiterRouter,
  job: jobRouter,
  dashboard: dashboardRouter,
  portalRequest: portalRequestRouter,
  member: memberRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
