import { TRPCError } from '@trpc/server';
import { z } from 'zod';

import { auth } from '@/auth';
import { connectToDatabase } from '@/db';
import User from '@/db/schema/user';
import { verifyHOTP } from '@/lib/otpauth';

import { createTRPCRouter, publicProcedure } from '../trpc';

export const adminAuthRouter = createTRPCRouter({
  verifyAdminTOtp: publicProcedure.input(z.string()).mutation(async ({ input }) => {
    try {
      const isValid = verifyHOTP(input);
      console.log('OTP verification result:', isValid);

      if (!isValid) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Invalid OTP. Please try again.',
        });
      }

      // Return success when OTP is valid
      return { success: true, message: 'OTP verified successfully' };
    } catch (error) {
      // Handle any errors from verifyHOTP or the TRPCError above
      if (error instanceof TRPCError) {
        throw error;
      }

      console.error('Error verifying OTP:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to verify OTP.',
      });
    }
  }),
  sendAdminEmailOtp: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .mutation(async ({ input }) => {
      const { email } = input;

      try {
        await connectToDatabase();
        const isUserExists = await User.exists({
          email: email,
          role: 'admin',
        });

        if (!isUserExists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'User not found. Please sign up first.',
          });
        }

        await auth.api.sendVerificationOTP({
          body: {
            email,
            type: 'sign-in',
          },
        });
        console.log('Sending OTP to email:', email);
        return { success: true };
      } catch (error) {
        console.error('Error sending OTP to email:', email, error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to send OTP. Please try again later.',
          cause: error,
        });
      }
    }),

  resendAdminEmailOtp: publicProcedure
    .input(z.object({ email: z.string().email() }))
    .mutation(async ({ input }) => {
      const { email } = input;

      try {
        await connectToDatabase();
        const isUserExists = await User.exists({
          email: email,
          role: 'admin',
        });

        if (!isUserExists) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'User not found. Please sign up first.',
          });
        }
        await auth.api.sendVerificationOTP({
          body: {
            email,
            type: 'sign-in',
          },
        });
        return { success: true };
      } catch (error) {
        console.error('Error resending OTP to email:', email, error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to resend OTP. Please try again later.',
          cause: error,
        });
      }
    }),
});
