import { TRPCError } from '@trpc/server';

import {
  createCandidateSchema,
  deleteCandidateSchema,
  getAllCandidatesSchema,
  getCandidateByEmailSchema,
  getCandidateByIdSchema,
  updateCandidateSchema,
} from '@/@types/candidate';
import User from '@/db/schema/user';
import { createTRPCRouter, protectedProcedure } from '@/server/api/trpc';

export const candidateRouter = createTRPCRouter({
  createCandidate: protectedProcedure.input(createCandidateSchema).mutation(async ({ input }) => {
    const { name, email, emailVerified, image } = input;

    // Check if user already exists with this email
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: 'A user with this email already exists',
      });
    }

    // Create new candidate (user with role 'user')
    const newCandidate = new User({
      name,
      email,
      emailVerified: emailVerified ?? false,
      image: image || '',
      role: 'user', // Candidates have role 'user'
    });

    await newCandidate.save();

    return newCandidate.toObject();
  }),

  getAllCandidates: protectedProcedure.input(getAllCandidatesSchema).query(async ({ input }) => {
    const { page, limit, search, sortBy, sortOrder } = input;

    // Calculate skip value for pagination
    const skip = (page - 1) * limit;

    // Build search query - only get users with role 'user' (candidates)
    const searchQuery: Record<string, unknown> = { role: 'user' };
    if (search) {
      searchQuery.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }

    // Build sort object
    const sortObject: Record<string, 1 | -1> = {};
    sortObject[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Execute queries in parallel for better performance
    const [candidates, totalCount] = await Promise.all([
      User.find(searchQuery).sort(sortObject).skip(skip).limit(limit),
      User.countDocuments(searchQuery),
    ]);

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return {
      data: candidates.map((candidate) => candidate.toObject()),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      },
    };
  }),

  getCandidateById: protectedProcedure
    .input(getCandidateByIdSchema)
    .query(async ({ input: id }) => {
      const candidate = await User.findOne({ _id: id, role: 'user' });
      if (!candidate) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Candidate not found',
        });
      }

      return candidate.toObject();
    }),

  getCandidateByEmail: protectedProcedure
    .input(getCandidateByEmailSchema)
    .query(async ({ input: email }) => {
      const candidate = await User.findOne({ email, role: 'user' });
      if (!candidate) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Candidate not found',
        });
      }

      return candidate.toObject();
    }),

  updateCandidate: protectedProcedure.input(updateCandidateSchema).mutation(async ({ input }) => {
    const { id, name, email, emailVerified, image } = input;

    // Find the candidate by ID and ensure it's a user role
    const candidate = await User.findOne({ _id: id, role: 'user' });
    if (!candidate) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'Candidate not found',
      });
    }

    // Check if email is being updated and if it already exists
    if (email && email !== candidate.email) {
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'A user with this email already exists',
        });
      }
    }

    // Update the candidate fields
    candidate.name = name || candidate.name;
    candidate.email = email || candidate.email;
    candidate.emailVerified = emailVerified ?? candidate.emailVerified;
    candidate.image = image || candidate.image;

    await candidate.save();

    return candidate.toObject();
  }),

  deleteCandidate: protectedProcedure
    .input(deleteCandidateSchema)
    .mutation(async ({ input: id }) => {
      const candidate = await User.findOne({ _id: id, role: 'user' });
      if (!candidate) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Candidate not found',
        });
      }

      await User.findByIdAndDelete(id);

      return {
        success: true,
        message: 'Candidate deleted successfully',
      };
    }),
});
