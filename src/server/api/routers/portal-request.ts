import { TRPCError } from '@trpc/server';

import {
  getAllPortalRequestsSchema,
  getPortalRequestByIdSchema,
  requestAccessSchema,
} from '@/@types/portal-request-access';
import { PortalRequestAccess } from '@/db/schema/portal-request-access';
import User from '@/db/schema/user';

import { createTRPCRouter, protectedProcedure, publicProcedure } from '../trpc';

export const portalRequestRouter = createTRPCRouter({
  createPortalRequest: publicProcedure.input(requestAccessSchema).mutation(async ({ input }) => {
    // Handle the creation of the portal request

    const existsEmailOnRequests = await PortalRequestAccess.exists({
      work_email: input.work_email,
    });
    if (existsEmailOnRequests) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: `A request with the email '${input.work_email}' already exists.`,
      });
    }
    const emailExists = await User.exists({
      email: input.work_email,
    });
    if (emailExists) {
      throw new TRPCError({
        code: 'CONFLICT',
        message: `A account with the email '${input.work_email}' already exists.`,
      });
    }

    const newRequest = new PortalRequestAccess({
      full_name: input.full_name,
      work_email: input.work_email,
      job_title: input.job_title,
      phone_number: input.phone_number,
      company_name: input.company_name,
      company_size: input.company_size,
      industry: input.industry,
      monthly_hires: input.monthly_hires,
      hiring_challenge: input.hiring_challenge,
      referral_source: input.referral_source || '',
    });
    await newRequest.save();

    return newRequest.toObject();
  }),

  getAllPortalRequests: protectedProcedure
    .input(getAllPortalRequestsSchema)
    .query(async ({ input }) => {
      const { page, limit, search, sortBy, sortOrder } = input;

      // Calculate skip value for pagination
      const skip = (page - 1) * limit;

      // Build search query
      const searchQuery: Record<string, unknown> = {};
      if (search) {
        searchQuery.$or = [
          { full_name: { $regex: search, $options: 'i' } },
          { work_email: { $regex: search, $options: 'i' } },
          { job_title: { $regex: search, $options: 'i' } },
          { company_name: { $regex: search, $options: 'i' } },
        ];
      }

      // Validate sortBy parameter
      const validSortByValues = [
        'createdAt',
        'updatedAt',
        'full_name',
        'work_email',
        'company_name',
        'company_size',
        'industry',
        'status',
      ];
      if (!validSortByValues.includes(sortBy)) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: `Invalid sortBy value. Expected one of ${validSortByValues.join(', ')}, received '${sortBy}'.`,
        });
      }

      // Fetch portal requests with pagination and sorting
      const requests = await PortalRequestAccess.find(searchQuery)
        .sort({ [sortBy]: sortOrder === 'asc' ? 1 : -1 })
        .skip(skip)
        .limit(limit)
        .exec();

      // Count total documents matching the search query
      const totalCount = await PortalRequestAccess.countDocuments(searchQuery).exec();
      const totalPages = Math.ceil(totalCount / limit);
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      return {
        data: requests,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNextPage,
          hasPreviousPage,
        },
      };
    }),

  getPortalRequestById: protectedProcedure
    .input(getPortalRequestByIdSchema)
    .query(async ({ input: id }) => {
      const request = await PortalRequestAccess.findById(id);
      if (!request) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Portal request with ID '${id}' not found.`,
        });
      }
      return request.toObject();
    }),
});
