import Job from '@/db/schema/job';
import Organization from '@/db/schema/organization';
import User from '@/db/schema/user';

import { protectedProcedure } from '../trpc';

export const dashboardRouter = {
  getDashboardStats: protectedProcedure.query(async ({ ctx }) => {
    const user = ctx.session.user;

    // Check if user is authenticated
    if (!user) {
      throw new Error('User not authenticated');
    }

    if (user.role === 'admin') {
      const totalUsers = await User.countDocuments({ role: 'user' });
      const totalRecruiters = await User.countDocuments({ role: 'recruiter' });
      const totalJobs = await Job.countDocuments();
      const totalOrganizations = await Organization.countDocuments();
      return {
        stats: {
          totalUsers,
          totalRecruiters,
          totalJobs,
          totalOrganizations,
        },
      };
    }
    if (user.role === 'recruiter') {
      const totalJobs = await Job.countDocuments({ recruiter: user.id });
      return {
        stats: {
          totalJobs,
        },
      };
    }
    return {
      stats: {},
    };
  }),
};
