import { TRPCError } from '@trpc/server';
import { z } from 'zod';

import { auth } from '@/auth';
import User from '@/db/schema/user';

import { createTRPCRouter, protectedProcedure } from '../trpc';

export const memberRouter = createTRPCRouter({
  // Send invitation to join organization
  sendInvitation: protectedProcedure
    .input(
      z.object({
        userId: z.string().min(1),
        organizationId: z.string().min(1),
        roles: z.array(z.enum(['admin', 'member', 'owner'])),
      })
    )
    .mutation(async ({ input }) => {
      const { userId, organizationId, roles } = input;

      try {
        const user = await User.findOne({
          _id: userId,
        });

        if (!user) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: `User with id '${userId}' does not exist.`,
          });
        }

        const userObj = user.toObject();

        // Use Better Auth's server-side API to invite member
        const invitation = await auth.api.addMember({
          body: {
            userId: userObj.id,
            organizationId,
            role: roles,
          },
        });

        return invitation;
      } catch (error: unknown) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error instanceof Error ? error.message : 'Failed to send invitation',
        });
      }
    }),

  // Accept invitation
  acceptInvitation: protectedProcedure
    .input(
      z.object({
        invitationId: z.string().min(1),
      })
    )
    .mutation(async ({ input }) => {
      const { invitationId } = input;

      try {
        // Use Better Auth's server-side API to accept invitation
        const result = await auth.api.acceptInvitation({
          body: {
            invitationId,
          },
        });

        return result;
      } catch (error: unknown) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error instanceof Error ? error.message : 'Failed to accept invitation',
        });
      }
    }),

  // Reject invitation
  rejectInvitation: protectedProcedure
    .input(
      z.object({
        invitationId: z.string().min(1),
      })
    )
    .mutation(async ({ input }) => {
      const { invitationId } = input;

      try {
        // Use Better Auth's server-side API to reject invitation
        const result = await auth.api.rejectInvitation({
          body: {
            invitationId,
          },
        });

        return result;
      } catch (error: unknown) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error instanceof Error ? error.message : 'Failed to reject invitation',
        });
      }
    }),

  // Cancel invitation
  cancelInvitation: protectedProcedure
    .input(
      z.object({
        invitationId: z.string().min(1),
      })
    )
    .mutation(async ({ input }) => {
      const { invitationId } = input;

      try {
        // Use Better Auth's server-side API to cancel invitation
        const result = await auth.api.cancelInvitation({
          body: {
            invitationId,
          },
        });

        return result;
      } catch (error: unknown) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error instanceof Error ? error.message : 'Failed to cancel invitation',
        });
      }
    }),

  // Update member role
  updateMemberRole: protectedProcedure
    .input(
      z.object({
        memberId: z.string().min(1),
        role: z.enum(['admin', 'member', 'owner']),
      })
    )
    .mutation(async ({ input, ctx }) => {
      const { memberId, role } = input;

      try {
        // Use Better Auth's server-side API to update member role
        const member = await auth.api.updateMemberRole({
          body: {
            memberId,
            role,
          },
          headers: ctx.headers,
        });

        return member;
      } catch (error: unknown) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error instanceof Error ? error.message : 'Failed to update member role',
        });
      }
    }),

  // Remove member from organization
  removeMember: protectedProcedure
    .input(
      z.object({
        memberIdOrEmail: z.string().min(1),
        organizationId: z.string().min(1),
      })
    )
    .mutation(async ({ input }) => {
      const { memberIdOrEmail, organizationId } = input;

      try {
        // Use Better Auth's server-side API to remove member
        await auth.api.removeMember({
          body: {
            memberIdOrEmail,
            organizationId,
          },
        });

        return {
          success: true,
          message: 'Member removed successfully',
        };
      } catch (error: unknown) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error instanceof Error ? error.message : 'Failed to remove member',
        });
      }
    }),

  // Leave organization
  leaveOrganization: protectedProcedure
    .input(
      z.object({
        organizationId: z.string().min(1),
      })
    )
    .mutation(async ({ input }) => {
      const { organizationId } = input;

      try {
        // Use Better Auth's server-side API to leave organization
        await auth.api.leaveOrganization({
          body: {
            organizationId,
          },
        });

        return {
          success: true,
          message: 'Left organization successfully',
        };
      } catch (error: unknown) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: error instanceof Error ? error.message : 'Failed to leave organization',
        });
      }
    }),

  // Get organization members
  getMembers: protectedProcedure
    .input(
      z.object({
        organizationId: z.string().min(1),
      })
    )
    .query(async ({ input }) => {
      const { organizationId: _orgId } = input;

      try {
        // Use Better Auth's helper to get members
        // const members = await auth.api.getMembers({
        //   query: {
        //     organizationId: _orgId,
        //   },
        // });

        return [];
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get members',
        });
      }
    }),

  // Get organization invitations
  getInvitations: protectedProcedure
    .input(
      z.object({
        organizationId: z.string().min(1),
      })
    )
    .query(async ({ input }) => {
      const { organizationId } = input;

      try {
        // Use Better Auth's server-side API to get invitations
        const invitations = await auth.api.listInvitations({
          query: {
            organizationId,
          },
        });

        return invitations;
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get invitations',
        });
      }
    }),

  // Get active member (current user's membership in the organization)
  getActiveMember: protectedProcedure
    .input(
      z.object({
        organizationId: z.string().min(1),
      })
    )
    .query(async ({ input }) => {
      const { organizationId } = input;

      try {
        // Use Better Auth's server-side API to get active member
        const member = await auth.api.getActiveMember({
          query: {
            organizationId,
          },
        });

        return member;
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get active member',
        });
      }
    }),
});
