import { TRPCError } from '@trpc/server';

import {
  createOrganizationSchema,
  getAllOrganizationsSchema,
  getOrganizationByIdSchema,
  getOrganizationBySlugSchema,
  getOrganizationMembersSchema,
  updateOrganizationSchema,
} from '@/@types/organization';
import Member from '@/db/schema/member';
import Organization from '@/db/schema/organization';

import { createTRPCRouter, protectedProcedure, publicProcedure } from '../trpc';

export const organizationRouter = createTRPCRouter({
  createOrganization: protectedProcedure
    .input(createOrganizationSchema)
    .mutation(async ({ input }) => {
      const { name, slug, description, logo, metadata } = input;
      // Check if the organization already exists
      const existsOrg = await Organization.exists({ slug });
      if (existsOrg) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: `Organization with slug '${slug}' already exists.`,
        });
      }
      // Create the new organization
      const newOrg = new Organization({
        name,
        slug,
        description: description || '',
        logo: logo || '',
        metadata: metadata || '',
      });
      await newOrg.save();
      return newOrg.toObject();
    }),

  getAllOrganizations: protectedProcedure
    .input(getAllOrganizationsSchema)
    .query(async ({ input, ctx }) => {
      const { page, limit, search, sortBy, sortOrder, organizationId } = input;

      // Calculate skip value for pagination
      const skip = (page - 1) * limit;

      const user = ctx.session?.user;
      const role = user?.role;

      // Build search query
      const searchQuery: Record<string, unknown> = {};
      if (search) {
        searchQuery.$or = [
          { name: { $regex: search, $options: 'i' } },
          { slug: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } },
        ];
      }

      if (role !== 'admin' || input.checkAdmin) {
        let selectOrganization = [];
        if (organizationId) {
          selectOrganization.push(organizationId);
        }
        const member = await Member.find({ userId: user?.id }).select('organizationId').lean();
        if (member.length > 0) {
          selectOrganization = member.map((m) => m.organizationId);
        }
        searchQuery._id = { $in: selectOrganization };
      }
      // Build sort object
      const sortObject: Record<string, 1 | -1> = {};
      sortObject[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Execute queries in parallel for better performance
      const [organizations, totalCount] = await Promise.all([
        Organization.find(searchQuery).sort(sortObject).skip(skip).limit(limit),
        Organization.countDocuments(searchQuery),
      ]);

      // Calculate pagination metadata
      const totalPages = Math.ceil(totalCount / limit);
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      return {
        data: organizations.map((org) => org.toObject()),
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNextPage,
          hasPreviousPage,
        },
      };
    }),

  getOrganizationBySlug: protectedProcedure
    .input(getOrganizationBySlugSchema)
    .query(async ({ input: slug }) => {
      const organization = await Organization.findOne({
        slug,
      });
      if (!organization) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Organization with slug '${slug}' not found.`,
        });
      }
      return organization.toObject();
    }),

  getPublicOrganizationDetailsBySlug: publicProcedure
    .input(getOrganizationBySlugSchema)
    .query(async ({ input: slug }) => {
      const organization = await Organization.findOne({
        slug,
      });

      return organization?.toObject();
    }),

  getOrganizationById: protectedProcedure
    .input(getOrganizationByIdSchema)
    .query(async ({ input: id }) => {
      const organization = await Organization.findById(id);
      if (!organization) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Organization with ID '${id}' not found.`,
        });
      }
      return organization.toObject();
    }),

  updateOrganization: protectedProcedure
    .input(updateOrganizationSchema)
    .mutation(async ({ input }) => {
      const { id, name, slug, description, logo, metadata } = input;

      // Find the organization by ID
      const organization = await Organization.findById(id);
      if (!organization) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: `Organization with ID '${id}' not found.`,
        });
      }
      // Check if the slug is being updated and if it already exists
      if (slug && slug !== organization.slug) {
        const existsOrg = await Organization.exists({ slug });
        if (existsOrg) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: `Organization with slug '${slug}' already exists.`,
          });
        }
      }
      // Update the organization fields
      organization.name = name || organization.name;
      organization.slug = slug || organization.slug;
      organization.description = description || organization.description;
      organization.logo = logo || organization.logo;
      organization.metadata = metadata || organization.metadata;
      await organization.save();
      return organization.toObject();
    }),

  // Get organization members
  getOrganizationMembers: protectedProcedure
    .input(getOrganizationMembersSchema)
    .query(async ({ input, ctx }) => {
      const { page, limit, search, sortBy, sortOrder, organizationId } = input;

      // Calculate skip value for pagination
      const skip = (page - 1) * limit;

      // Build search query - only get users with role 'recruiter'
      const searchQuery: Record<string, unknown> = { role: 'recruiter' };
      if (search) {
        searchQuery.$or = [
          { name: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } },
        ];
      }

      // Build sort object
      const sortObject: Record<string, 1 | -1> = {};
      sortObject[sortBy] = sortOrder === 'asc' ? 1 : -1;

      // Execute queries in parallel for better performance
      const [recruiters, totalCount] = await Promise.all([
        Member.find({ organizationId })
          .populate('userId', 'name email image role createdAt updatedAt')
          .sort(sortObject)
          .skip(skip)
          .limit(limit),
        Member.countDocuments({ organizationId }),
      ]);

      // Calculate pagination metadata
      const totalPages = Math.ceil(totalCount / limit);
      const hasNextPage = page < totalPages;
      const hasPreviousPage = page > 1;

      const user = recruiters.map((member) => member.userId);

      return {
        data: user,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNextPage,
          hasPreviousPage,
        },
      };
    }),
});
