import { betterAuth } from 'better-auth';
import { mongodbAdapter } from 'better-auth/adapters/mongodb';
import { nextCookies } from 'better-auth/next-js';
import { admin, openAPI } from 'better-auth/plugins';
import { api<PERSON>ey } from 'better-auth/plugins';
import { emailOTP } from 'better-auth/plugins';
import { magicLink } from 'better-auth/plugins';
import { bearer } from 'better-auth/plugins';
import { organization } from 'better-auth/plugins';
import { jwt } from 'better-auth/plugins';
// import { oneTap } from 'better-auth/plugins';
import { oneTimeToken } from 'better-auth/plugins/one-time-token';

import { dbClient } from '@/db';
import User from '@/db/schema/user';
import { env } from '@/env';
import { Role } from '@/lib/constants';
import { getAppUrl, getCookieDomains, getTrustedOrigins, isProduction } from '@/lib/domains';

export const auth = betterAuth({
  appName: env.APP_NAME,
  baseURL: getAppUrl(),
  database: mongodbAdapter(dbClient),
  trustedOrigins: getTrustedOrigins(),
  advanced: {
    cookiePrefix: '_hirelytics._cookie',
    useSecureCookies: isProduction(),
    crossSubDomainCookies: {
      enabled: false,
      domains: getCookieDomains(),
    },
    defaultCookieAttributes: {
      secure: isProduction(),
      httpOnly: true,
      sameSite: 'lax',
      partitioned: false,
    },
  },
  session: {
    expiresIn: 60 * 60 * 24 * 30, // 30 days
    cookie: {
      secure: false,
      sameSite: 'lax',
    },
    storage: 'database',
    preserveSessionInDatabase: true,
    modelName: 'session',
    cookieCache: {
      enabled: env.NODE_ENV === 'production',
      duration: 60 * 60 * 24 * 30, // 30 days
      maxAge: 60,
    },
  },
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // Limit each IP to 100 requests per `window` (here, per 15 minutes)
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false, // Disable the `X-RateLimit-*` headers
    storage: 'database',
    modelName: 'rate-limit',
  },
  user: {
    additionalFields: {
      role: {
        type: 'string',
        required: true,
        defaultValue: 'user',
        input: true, // allow user to set role
      },
    },
  },
  emailVerification: {
    sendVerificationEmail: async ({ user, url, token }, request) => {
      console.log({
        to: user.email,
        subject: 'Verify your email address',
        text: `Click the link to verify your email: ${url}`,
        html: `<a href="${url}">Click here to verify your email</a>`,
        token: token,
        request: request,
      });
    },
    autoSignInAfterVerification: true,
    sendOnSignUp: true,
  },
  emailAndPassword: {
    enabled: true,
    autoSignIn: true,
    resetPasswordTokenExpiresIn: 60 * 60, // 1 hour
    sendResetPassword: async ({ user, url, token }, request) => {
      console.log({
        to: user.email,
        subject: 'Reset your password',
        text: `Click the link to reset your password: ${url}`,
        html: `<a href="${url}">Click here to reset your password</a>`,
        token: token,
        request: request,
      });
    },
  },
  plugins: [
    nextCookies(), // Make sure this is first in the array
    admin({}),
    apiKey(),
    openAPI(),
    bearer({
      requireSignature: true,
    }),
    jwt(),
    oneTimeToken(),
    organization({
      allowUserToCreateOrganization: async (user) => {
        const userId = user.id;
        const isAdmin = await User.exists({
          _id: userId,
          role: Role.ADMIN,
        });
        return !!isAdmin;
      },
      sendInvitationEmail: async (data) => {
        console.log({
          email: data.email,
          invitedByUsername: data.inviter.user.name,
          invitedByEmail: data.inviter.user.email,
          teamName: data.organization.name,
          invitationId: data.id,
        });
      },
    }),
    // oneTap(),
    emailOTP({
      async sendVerificationOTP({ email, otp, type }) {
        if (type === 'sign-in') {
          // Send the OTP for sign-in
        } else if (type === 'email-verification') {
          // Send the OTP for email verification
        } else {
          // Send the OTP for password reset
        }
        console.log(`Sending OTP ${otp} to ${email} for ${type}`);
      },
    }),
    magicLink({
      sendMagicLink: async ({ email, token, url }, request) => {
        // send email to user
        console.log(
          `Sending magic link to ${email} with token ${token} at ${url} for request ${request}`
        );
      },
    }),
  ],

  socialProviders: {
    // github: {
    //   enabled: true,
    //   clientId: env.GITHUB_CLIENT_ID,
    //   clientSecret: env.GITHUB_CLIENT_SECRET,
    //   autoSignIn: true,
    // },
    google: {
      enabled: true,
      clientId: env.GOOGLE_CLIENT_ID,
      clientSecret: env.GOOGLE_CLIENT_SECRET,
      autoSignIn: true,
    },
  },
  // account: {
  //   accountLinking: {
  //     trustedProviders: ['github'],
  //   },
  // },
});

export type Session = typeof auth.$Infer.Session;
export type ActiveOrganization = typeof auth.$Infer.ActiveOrganization;
export type Invitation = typeof auth.$Infer.Invitation;
export type Member = typeof auth.$Infer.Member;
export type Organization = typeof auth.$Infer.Organization;
export type Team = typeof auth.$Infer.Team;
