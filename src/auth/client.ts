import { emailOTPClient, inferAdditionalFields } from 'better-auth/client/plugins';
import { createAuthClient, ErrorContext } from 'better-auth/react';
import { toast } from 'sonner';

import { getBaseUrl } from '@/lib/domains';

import { auth } from '.';

export const authClient = createAuthClient({
  basePath: '/api/auth',
  baseURL: getBaseUrl(),
  plugins: [inferAdditionalFields<typeof auth>(), emailOTPClient()],
  fetchOptions: {
    credentials: 'include', // Important: Include credentials for cookie handling
    onSuccess: () => {
      toast.success('Success!');
    },
    onError: (e: ErrorContext) => {
      if (e.error.status === 400) {
        toast.error(e.error.message);
      }
      if (e.error.status === 401) {
        toast.error("You're not authorized to perform this action.");
      }
      if (e.error.status === 403) {
        toast.error("You're not authorized to perform this action.");
      }
      if (e.error.status === 404) {
        toast.error('Resource not found.');
      }
      if (e.error.status === 422) {
        toast.error('Validation error. Please check your input.');
      }
      if (e.error.status === 429) {
        toast.error("You've made too many requests. Please try again later.");
      }
      if (e.error.status === 500) {
        toast.error('Something went wrong. Please try again later.');
      }
    },
  },
});

const {
  signIn,
  signOut,
  signUp,
  useSession,
  forgetPassword,
  resetPassword,
  sendVerificationEmail,
} = authClient;

export {
  forgetPassword,
  resetPassword,
  sendVerificationEmail,
  signIn,
  signOut,
  signUp,
  useSession,
};
