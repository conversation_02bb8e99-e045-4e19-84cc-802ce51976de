import { env } from '@/env';

/**
 * Get the current app URL based on environment
 */
export function getAppUrl(): string {
  // if (env.NODE_ENV === 'production') {
  //   return env.AUTH_DOMAIN;
  // }
  return env.AUTH_DOMAIN;
}

/**
 * Get the current app domain based on environment
 */
export function getAppDomain(): string {
  if (env.NODE_ENV === 'production') {
    return env.PRODUCTION_APP_DOMAIN;
  }
  return env.APP_DOMAIN;
}

/**
 * Get trusted origins for the current environment
 */
export function getTrustedOrigins(): string[] {
  return env.DEV_TRUSTED_ORIGINS.split(',').map((origin) => origin.trim());
}

/**
 * Get cookie domains for the current environment
 */
export function getCookieDomains(): string[] {
  return env.DEV_COOKIE_DOMAINS.split(',').map((domain) => domain.trim());
}

/**
 * Check if running in production environment
 */
export function isProduction(): boolean {
  return env.NODE_ENV === 'production';
}

/**
 * Check if running in development environment
 */
export function isDevelopment(): boolean {
  return env.NODE_ENV === 'development';
}

/**
 * Get the base URL for API calls
 */
export function getBaseUrl(): string {
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  return getAppUrl();
}
