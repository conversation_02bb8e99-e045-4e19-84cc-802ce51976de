import { difficultyLevels } from '@/app/console/(dashboard)/jobs/_components/data/job-form-constants';

/**
 * Get difficulty level display information
 * @param level - The difficulty level value
 * @returns Object with label, description, and color class
 */
export function getDifficultyLevelInfo(level: string) {
  const difficultyInfo = difficultyLevels.find((item) => item.value === level);

  if (!difficultyInfo) {
    return {
      label: 'Normal',
      description: 'Standard questions for intermediate-level positions',
      colorClass:
        'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:border-blue-800',
    };
  }

  // Define color classes for each difficulty level with better contrast
  const colorClasses = {
    easy: 'bg-emerald-100 text-emerald-800 border-emerald-200 dark:bg-emerald-900 dark:text-emerald-200 dark:border-emerald-800',
    normal:
      'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:border-blue-800',
    hard: 'bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-900 dark:text-amber-200 dark:border-amber-800',
    expert:
      'bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-200 dark:border-red-800',
    advanced:
      'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900 dark:text-purple-200 dark:border-purple-800',
  };

  return {
    label: difficultyInfo.label,
    description: difficultyInfo.description,
    colorClass: colorClasses[level as keyof typeof colorClasses] || colorClasses.normal,
  };
}

/**
 * Get difficulty level icon
 * @param level - The difficulty level value
 * @returns Icon component or string
 */
export function getDifficultyLevelIcon(level: string) {
  const icons = {
    easy: '🟢',
    normal: '🔵',
    hard: '🟠',
    expert: '🔴',
    advanced: '🟣',
  };

  return icons[level as keyof typeof icons] || icons.normal;
}
