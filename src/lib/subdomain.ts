/**
 * Utility functions for working with subdomains in the Hirelytics app
 */
import { env } from '@/env';

/**
 * Main application domain - used throughout the application
 */
export const APP_DOMAIN = env.NEXT_PUBLIC_APP_DOMAIN;

/**
 * Subdomain configuration types
 */
export interface SubdomainConfig {
  name: string;
  description: string;
  isSystem: boolean;
  routePrefix: string;
}

/**
 * System subdomain configurations
 */
export const SUBDOMAIN_CONFIGS: Record<string, SubdomainConfig> = {
  auth: {
    name: 'Authentication',
    description: 'User authentication and registration',
    isSystem: true,
    routePrefix: '/auth',
  },
  console: {
    name: 'Console',
    description: 'Administrative dashboard and tools',
    isSystem: true,
    routePrefix: '/console',
  },
  www: {
    name: 'Main Site',
    description: 'Main landing page',
    isSystem: true,
    routePrefix: '/',
  },
  api: {
    name: 'API',
    description: 'Application programming interface',
    isSystem: true,
    routePrefix: '/api',
  },
  app: {
    name: 'Application',
    description: 'Main application',
    isSystem: true,
    routePrefix: '/app',
  },
};

/**
 * List of system subdomains that are reserved
 */
export const SYSTEM_SUBDOMAINS = Object.keys(SUBDOMAIN_CONFIGS).filter(
  (key) => SUBDOMAIN_CONFIGS[key].isSystem
);

/**
 * Get the current subdomain from the hostname
 */
export function getSubdomain(hostname: string): string | null {
  // Handle localhost development
  if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
    // For localhost without any dots, it's the root domain
    if (!hostname.includes('.')) {
      return null;
    }

    const subdomain = hostname.split('.')[0];
    if (subdomain === 'localhost' || subdomain.includes('127.0.0.1')) {
      return null; // No subdomain for root localhost
    }
    return subdomain;
  }

  // Handle production domains
  const appDomain = APP_DOMAIN;
  const parts = hostname.split('.');

  // Check if there's a subdomain
  if (hostname.endsWith(appDomain) && parts.length > 2) {
    return parts[0];
  }

  // Handle www as no subdomain
  if (parts.length === 2 && parts[0] === 'www') {
    return null;
  }

  return null;
}

/**
 * Check if the current subdomain is a system subdomain
 */
export function isSystemSubdomain(subdomain: string | null): boolean {
  if (!subdomain) return false;
  return SYSTEM_SUBDOMAINS.includes(subdomain);
}

/**
 * Get the configuration for a subdomain
 */
export function getSubdomainConfig(subdomain: string | null) {
  if (!subdomain) return null;
  return SUBDOMAIN_CONFIGS[subdomain] || null;
}

/**
 * Get the full domain for a subdomain, including the protocol
 * @param subdomain The subdomain to get the full domain for
 * @param isSecure Whether to use HTTPS or HTTP
 */
export function getFullDomain(subdomain: string | null, isSecure = false): string {
  const protocol = isSecure ? 'https://' : 'http://';

  if (!subdomain) {
    return `${protocol}${APP_DOMAIN}`;
  }

  return `${protocol}${subdomain}.${APP_DOMAIN}`;
}

/**
 * Get the route prefix for a subdomain
 * @param subdomain The subdomain to get the route prefix for
 */
export function getRoutePrefix(subdomain: string | null): string {
  if (!subdomain) return '/';

  const config = SUBDOMAIN_CONFIGS[subdomain];
  if (config) return config.routePrefix;

  // For dynamic subdomains
  return `/dynamic/${subdomain}`;
}

/**
 * Build a URL for a specific subdomain
 */
export function getSubdomainUrl(subdomain: string | null, path: string = '/'): string {
  return getFullDomain(subdomain, !isLocalDomain(APP_DOMAIN)) + path;
}

/**
 * Check if domain is localhost/development
 */
function isLocalDomain(domain: string): boolean {
  return domain.includes('localhost') || domain.includes('127.0.0.1');
}

/**
 * Get the route handler path for a subdomain
 */
export function getSubdomainRoutePath(subdomain: string | null): string {
  return getRoutePrefix(subdomain);
}
