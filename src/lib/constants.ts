/**
 * Application-wide constants and configuration values
 */
import { env } from '@/env';

/**
 * System subdomains that are reserved
 */
export const SYSTEM_SUBDOMAINS = ['auth', 'console', 'www', 'api', 'app'];

/**
 * Main application domain - used throughout the application
 */
export const APP_DOMAIN = env.NEXT_PUBLIC_APP_DOMAIN;

/**
 * Get the full domain for a subdomain, including the protocol
 * @param subdomain The subdomain to get the full domain for
 * @param isSecure Whether to use HTTPS or HTTP
 */
export function getFullDomain(subdomain: string | null, isSecure = false): string {
  const protocol = isSecure ? 'https://' : 'http://';

  if (!subdomain) {
    return `${protocol}${APP_DOMAIN}`;
  }

  return `${protocol}${subdomain}.${APP_DOMAIN}`;
}

export const Role = {
  ADMIN: 'admin',
  CANDIDATE: 'user',
  RECRUITER: 'recruiter',
};
