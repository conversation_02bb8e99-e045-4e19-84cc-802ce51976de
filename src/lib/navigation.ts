import {
  IconBasketQuestion,
  IconBriefcase,
  IconBuilding,
  IconDashboard,
  IconUsers,
} from '@tabler/icons-react';

import { UserRole } from '../contexts/role-context';

export interface NavItem {
  title: string;
  url: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  icon: any;
  isActive?: boolean;
  color?: string;
}

export interface NavGroup {
  title?: string;
  items: NavItem[];
}

// const commonItems: NavItem[] = [
//   {
//     title: 'Dashboard',
//     url: '/',
//     icon: IconDashboard,
//     color: 'text-blue-500',
//   },
//   {
//     title: 'Profile',
//     url: '/profile',
//     icon: IconUser,
//     color: 'text-green-500',
//   },
//   {
//     title: 'Settings',
//     url: '/settings',
//     icon: IconSettings,
//     color: 'text-gray-500',
//   },
//   {
//     title: 'Notifications',
//     url: '/notifications',
//     icon: IconBell,
//     color: 'text-yellow-500',
//   },
//   {
//     title: 'Help',
//     url: '/help',
//     icon: IconHelp,
//     color: 'text-purple-500',
//   },
// ];

// const managementRecruitmentItems: NavItem[] = [
//   {
//     title: 'Candidates',
//     url: '/candidates',
//     icon: IconUsers,
//     color: 'text-indigo-500',
//   },

//   {
//     title: 'Applications',
//     url: '/applications',
//     icon: IconFileText,
//     color: 'text-teal-500',
//   },
//   {
//     title: 'Interviews',
//     url: '/interviews',
//     icon: IconCalendar,
//     color: 'text-pink-500',
//   },
//   {
//     title: 'Organizations',
//     url: '/organizations',
//     icon: IconBuilding,
//     color: 'text-cyan-500',
//   },
//   {
//     title: 'Teams',
//     url: '/teams',
//     icon: IconUsersGroup,
//     color: 'text-emerald-500',
//   },
// ];

// const analyticsItems: NavItem[] = [
//   {
//     title: 'Reports',
//     url: '/reports',
//     icon: IconChart,
//     color: 'text-red-500',
//   },
//   {
//     title: 'Analytics',
//     url: '/analytics',
//     icon: IconAnalyze,
//     color: 'text-violet-500',
//   },
// ];

// const candidateFeatureItems: NavItem[] = [
//   {
//     title: 'Applied Jobs',
//     url: '/applied-jobs',
//     icon: IconFileText,
//     color: 'text-slate-500',
//   },
//   {
//     title: 'Saved Jobs',
//     url: '/saved-jobs',
//     icon: IconHeart,
//     color: 'text-rose-500',
//   },
// ];

export const getNavigationForRole = (_role: UserRole): NavGroup[] => {
  // switch (role) {
  //   case 'admin':
  //     return [
  //       {
  //         title: 'Main',
  //         items: commonItems,
  //       },
  //       {
  //         title: 'Management',
  //         items: managementRecruitmentItems,
  //       },
  //       {
  //         title: 'Analytics',
  //         items: analyticsItems,
  //       },
  //       {
  //         title: 'Candidate Features',
  //         items: candidateFeatureItems,
  //       },
  //     ];

  //   case 'recruiter':
  //     return [
  //       {
  //         title: 'Main',
  //         items: commonItems,
  //       },
  //       {
  //         title: 'Recruitment',
  //         items: managementRecruitmentItems,
  //       },
  //       {
  //         title: 'Analytics',
  //         items: analyticsItems,
  //       },
  //     ];

  //   case 'candidate':
  //     return [
  //       {
  //         title: 'Main',
  //         items: [
  //           {
  //             title: 'Dashboard',
  //             url: '/',
  //             icon: IconDashboard,
  //             color: 'text-blue-500',
  //           },
  //           {
  //             title: 'Job Search',
  //             url: '/job-search',
  //             icon: IconSearch,
  //             color: 'text-emerald-500',
  //           },
  //           {
  //             title: 'Applied Jobs',
  //             url: '/applied-jobs',
  //             icon: IconFileText,
  //             color: 'text-orange-500',
  //           },
  //           {
  //             title: 'Saved Jobs',
  //             url: '/saved-jobs',
  //             icon: IconHeart,
  //             color: 'text-rose-500',
  //           },
  //           {
  //             title: 'Interviews',
  //             url: '/interviews',
  //             icon: IconCalendar,
  //             color: 'text-pink-500',
  //           },
  //           {
  //             title: 'Profile',
  //             url: '/profile',
  //             icon: IconUser,
  //             color: 'text-green-500',
  //           },
  //           {
  //             title: 'Settings',
  //             url: '/settings',
  //             icon: IconSettings,
  //             color: 'text-gray-500',
  //           },
  //           {
  //             title: 'Notifications',
  //             url: '/notifications',
  //             icon: IconBell,
  //             color: 'text-yellow-500',
  //           },
  //           {
  //             title: 'Help',
  //             url: '/help',
  //             icon: IconHelp,
  //             color: 'text-purple-500',
  //           },
  //         ],
  //       },
  //     ];

  //   default:
  //     return [{ items: commonItems }];
  // }

  return [
    {
      title: 'Main',
      items: [
        {
          title: 'Dashboard',
          url: '/',
          icon: IconDashboard,
          color: 'text-blue-500',
        },
        {
          title: 'Organizations',
          url: '/organizations',
          icon: IconBuilding,
          color: 'text-cyan-500',
        },
        {
          title: 'Candidates',
          url: '/candidates',
          icon: IconUsers,
          color: 'text-indigo-500',
        },
        {
          title: 'Recruiters',
          url: '/recruiters',
          icon: IconUsers,
          color: 'text-teal-500',
        },
        {
          title: 'Jobs',
          url: '/jobs',
          icon: IconBriefcase,
          color: 'text-orange-500',
        },
        {
          title: 'Portal Requests',
          url: '/portal-requests',
          icon: IconBasketQuestion,
          color: 'text-orange-500',
        },
      ],
    },
  ];
};
