import { LucideIcon } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { ReactNode } from 'react';

import { AuthCard } from '@/components/ui/auth-card';
import { Button } from '@/components/ui/button';
import { RadialGradientBackground } from '@/components/ui/radial-gradient-background';

interface ErrorPageProps {
  title: string;
  description: string;
  errorCode?: string | number;
  icon: LucideIcon;
  iconGradient: string;
  codeGradient: string;
  actions?: ReactNode;
  showLogo?: boolean;
}

export function ErrorPage({
  title,
  description,
  errorCode,
  icon: Icon,
  iconGradient,
  codeGradient,
  actions,
  showLogo = true,
}: ErrorPageProps) {
  return (
    <RadialGradientBackground>
      <div className="flex w-full max-w-lg flex-col gap-8">
        {/* Logo section */}
        {showLogo && (
          <div className="text-center">
            <Image
              src="/images/hirelytics-full-logo.svg"
              alt="Hirelytics"
              width={200}
              height={180}
              className="mx-auto"
            />
          </div>
        )}

        <AuthCard title={title} description={description} className="text-center">
          <div className="flex flex-col items-center gap-6">
            {/* Error Icon */}
            <div
              className={`flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r ${iconGradient}`}
            >
              <Icon className="w-10 h-10" />
            </div>

            {/* Error Code */}
            {errorCode && (
              <div className="text-center">
                <h1
                  className={`text-6xl font-bold bg-gradient-to-r ${codeGradient} bg-clip-text text-transparent mb-2`}
                >
                  {errorCode}
                </h1>
              </div>
            )}

            {/* Custom Actions or Default Actions */}
            {actions || (
              <div className="flex flex-col sm:flex-row gap-3 w-full">
                <Link href="/" className="w-full">
                  <Button className="w-full h-11 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 dark:from-blue-500 dark:to-blue-600 dark:hover:from-blue-600 dark:hover:to-blue-700 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-[1.02] text-white">
                    Go Home
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </AuthCard>
      </div>
    </RadialGradientBackground>
  );
}

// Pre-configured error page variants
export const ErrorPageVariants = {
  notFound: {
    iconGradient: 'from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30',
    codeGradient: 'from-orange-600 to-red-600 dark:from-orange-400 dark:to-red-400',
    iconColor: 'text-orange-600 dark:text-orange-400',
  },
  serverError: {
    iconGradient: 'from-red-100 to-pink-100 dark:from-red-900/30 dark:to-pink-900/30',
    codeGradient: 'from-red-600 to-pink-600 dark:from-red-400 dark:to-pink-400',
    iconColor: 'text-red-600 dark:text-red-400',
  },
  forbidden: {
    iconGradient: 'from-red-100 to-orange-100 dark:from-red-900/30 dark:to-orange-900/30',
    codeGradient: 'from-red-600 to-orange-600 dark:from-red-400 dark:to-orange-400',
    iconColor: 'text-red-600 dark:text-red-400',
  },
  unauthorized: {
    iconGradient: 'from-amber-100 to-orange-100 dark:from-amber-900/30 dark:to-orange-900/30',
    codeGradient: 'from-amber-600 to-orange-600 dark:from-amber-400 dark:to-orange-400',
    iconColor: 'text-amber-600 dark:text-amber-400',
  },
};
