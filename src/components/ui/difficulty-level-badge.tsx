import { getDifficultyLevelIcon, getDifficultyLevelInfo } from '@/lib/interview-utils';

interface DifficultyLevelBadgeProps {
  level: string;
  showIcon?: boolean;
  showDescription?: boolean;
  size?: 'sm' | 'default' | 'lg';
}

export function DifficultyLevelBadge({
  level,
  showIcon = true,
  showDescription = false,
  size = 'default',
}: DifficultyLevelBadgeProps) {
  const { label, description, colorClass } = getDifficultyLevelInfo(level);
  const icon = getDifficultyLevelIcon(level);

  return (
    <div className="flex items-center gap-2">
      <div
        className={`inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ${colorClass} ${
          size === 'sm'
            ? 'text-xs px-2 py-0.5'
            : size === 'lg'
              ? 'text-sm px-3 py-1'
              : 'text-xs px-2.5 py-0.5'
        }`}
      >
        {showIcon && <span className="mr-1.5">{icon}</span>}
        {label}
      </div>
      {showDescription && (
        <span className="text-sm text-muted-foreground italic">{description}</span>
      )}
    </div>
  );
}

export default DifficultyLevelBadge;
