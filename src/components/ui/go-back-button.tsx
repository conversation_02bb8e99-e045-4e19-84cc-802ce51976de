'use client';

import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';

interface GoBackButtonProps {
  children: React.ReactNode;
  className?: string;
}

export function GoBackButton({ children, className }: GoBackButtonProps) {
  const router = useRouter();

  return (
    <Button onClick={() => router.back()} variant="outline" className={className}>
      {children}
    </Button>
  );
}
