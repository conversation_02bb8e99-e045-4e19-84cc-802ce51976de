'use client';
import {
  type ColumnDef,
  type ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  type Row,
  useReactTable,
  type VisibilityState,
} from '@tanstack/react-table';
import { TRPCClientErrorLike } from '@trpc/client';
import { ArrowUpDown, ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import React, { type ReactNode, useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useIsMobile } from '@/hooks/use-mobile';
import { useTableState } from '@/hooks/use-table-state';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';

// Mobile Card Component for responsive table
function MobileCard<TData>({
  row,
  showBorder = true,
  spacing = 'normal',
  hideActionColumns = false,
}: {
  row: Row<TData>;
  showBorder?: boolean;
  spacing?: 'tight' | 'normal' | 'loose';
  hideActionColumns?: boolean;
}) {
  const spacingClasses = {
    tight: 'p-2',
    normal: 'p-4',
    loose: 'p-6',
  };

  const itemSpacingClasses = {
    tight: 'py-1',
    normal: 'py-2',
    loose: 'py-3',
  };

  return (
    <Card className="mb-4 w-full shadow-sm border">
      <CardContent className={`${spacingClasses[spacing]} space-y-2`}>
        {row.getVisibleCells().map((cell) => {
          const column = cell.column;
          const columnDef = column.columnDef;

          // Get header text from various possible sources
          let headerText = '';

          if (typeof columnDef.header === 'string') {
            headerText = columnDef.header;
          } else if (typeof columnDef.header === 'function') {
            // For SortableHeader components, create a mapping based on accessorKey
            if ('accessorKey' in columnDef && columnDef.accessorKey) {
              const accessorKey = String(columnDef.accessorKey);
              // Create nice display names for common fields
              const fieldNameMap: Record<string, string> = {
                name: 'Organization',
                slug: 'Slug',
                createdAt: 'Created',
                updatedAt: 'Updated',
                email: 'Email',
                phone: 'Phone',
                status: 'Status',
                role: 'Role',
                title: 'Title',
                description: 'Description',
              };

              headerText =
                fieldNameMap[accessorKey] ||
                accessorKey.charAt(0).toUpperCase() + accessorKey.slice(1);
            } else {
              headerText = column.id || 'Action';
            }
          } else if ('accessorKey' in columnDef && columnDef.accessorKey) {
            // Use accessor key as fallback
            headerText =
              typeof columnDef.accessorKey === 'string'
                ? columnDef.accessorKey.charAt(0).toUpperCase() + columnDef.accessorKey.slice(1)
                : 'Column';
          } else {
            // Last resort - use column id
            headerText = column.id || 'Action';
          }

          // Check if this is an action column
          const isActionColumn =
            headerText === 'Action' ||
            headerText === 'actions' ||
            !('accessorKey' in columnDef) ||
            !columnDef.accessorKey;

          // Skip action columns if hideActionColumns is true
          if (hideActionColumns && isActionColumn) {
            return null;
          }

          // Handle action columns (typically at the end)
          if (isActionColumn) {
            return (
              <div key={cell.id} className="flex justify-end mt-2">
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </div>
            );
          }

          const borderClass = showBorder ? 'border-b border-border last:border-b-0' : '';

          return (
            <div
              key={cell.id}
              className={`grid grid-cols-[auto_auto_1fr] gap-3 items-start ${itemSpacingClasses[spacing]} ${borderClass}`}
            >
              <div className="text-sm font-medium text-foreground min-w-0">{headerText}</div>
              <span className="text-sm text-muted-foreground select-none">:</span>
              <div className="text-sm text-foreground break-words overflow-hidden min-w-0">
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </div>
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}

// Generic types for table functionality
export interface PaginationMeta {
  page: number;
  limit: number;
  totalCount: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface TableWrapperProps<TData> {
  columns: ColumnDef<TData>[];
  data: TData[];
  pagination?: PaginationMeta;
  isLoading?: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  error?: Error | null | TRPCClientErrorLike<any>;
  searchPlaceholder?: string;
  enableSearch?: boolean;
  enableLimitSelector?: boolean;
  limitOptions?: number[];
  onRetry?: () => void;
  children?: ReactNode;
  // Mobile-specific customization
  mobileCardCustomization?: {
    showBorder?: boolean;
    spacing?: 'tight' | 'normal' | 'loose';
    hideActionColumns?: boolean;
  };
}

export function TableWrapper<TData>({
  columns,
  data,
  pagination,
  isLoading = false,
  error = null,
  searchPlaceholder = 'Search...',
  enableSearch = true,
  enableLimitSelector = true,
  limitOptions = [10, 25, 50, 100],
  onRetry,
  children,
  mobileCardCustomization = {},
}: TableWrapperProps<TData>) {
  const { page, limit, search, setPage, setLimit, setSearch } = useTableState();
  const [localSearch, setLocalSearch] = useState(search);
  const isMobile = useIsMobile();

  // Force mobile view for testing - remove this line later
  const forceMobile = typeof window !== 'undefined' && window.innerWidth < 768;
  const shouldShowMobile = isMobile || forceMobile;

  const {
    showBorder = true,
    spacing = 'normal',
    hideActionColumns = false,
  } = mobileCardCustomization;

  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  // For database-level operations, we don't need client-side sorting/pagination/filtering
  const table = useReactTable({
    data,
    columns,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    // Server-side pagination means we don't use client-side pagination
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
    state: {
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  // Debounce search updates
  useEffect(() => {
    setLocalSearch(search);
  }, [search]);

  useEffect(() => {
    const timer = setTimeout(() => {
      if (localSearch !== search) {
        setSearch(localSearch);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [localSearch, search, setSearch]);

  if (error) {
    return (
      <div className="flex flex-col items-center gap-4 p-8">
        <p className="text-destructive">Error loading data: {error.message}</p>
        {onRetry && <Button onClick={onRetry}>Try Again</Button>}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search and Controls */}
      {(enableSearch || enableLimitSelector) && (
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          {enableSearch && (
            <div className="flex-1 max-w-sm">
              <Input
                placeholder={searchPlaceholder}
                value={localSearch}
                onChange={(e) => setLocalSearch(e.target.value)}
              />
            </div>
          )}
          {/* <div className="flex items-center py-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="ml-auto">
                  Columns <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {table
                  .getAllColumns()
                  .filter((column) => column.getCanHide())
                  .map((column) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={column.id}
                        className="capitalize"
                        checked={column.getIsVisible()}
                        onCheckedChange={(value) => column.toggleVisibility(!!value)}
                      >
                        {column.id}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </DropdownMenuContent>
            </DropdownMenu>
          </div> */}
        </div>
      )}

      {/* Custom children (e.g., additional filters) */}
      {children}

      {/* Data Table */}
      {/* <DataTable columns={columns} data={data} /> */}

      <div className="w-full">
        {shouldShowMobile ? (
          // Mobile rendering with cards
          <div className="space-y-4">
            {table.getRowModel().rows?.length ? (
              table
                .getRowModel()
                .rows.map((row) => (
                  <MobileCard
                    key={row.id}
                    row={row}
                    showBorder={showBorder}
                    spacing={spacing}
                    hideActionColumns={hideActionColumns}
                  />
                ))
            ) : (
              <div className="text-center p-8 text-muted-foreground">
                {localSearch ? 'No results found matching your search.' : 'No data available.'}
              </div>
            )}
          </div>
        ) : (
          // Desktop table rendering
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(header.column.columnDef.header, header.getContext())}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow key={row.id} data-state={row.getIsSelected() && 'selected'}>
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>
                          {flexRender(cell.column.columnDef.cell, cell.getContext())}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </div>

      {/* Loading Indicator */}
      {isLoading && (
        <div className="flex justify-center p-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </div>
      )}

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          {/* Results Summary */}
          <div className="text-sm text-muted-foreground order-2 lg:order-1">
            Showing {(pagination.page - 1) * pagination.limit + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.totalCount)} of{' '}
            {pagination.totalCount} results
          </div>

          {/* Pagination Controls */}
          <div className="flex flex-col gap-3 order-1 lg:order-2 sm:flex-row sm:items-center sm:justify-between lg:justify-end">
            {/* Items per page selector */}
            {enableLimitSelector && (
              <div className="flex items-center gap-2 justify-center sm:justify-start">
                <span className="text-sm text-muted-foreground hidden sm:inline">Show:</span>
                <Select
                  value={limit.toString()}
                  onValueChange={(value) => setLimit(parseInt(value))}
                >
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {limitOptions.map((option) => (
                      <SelectItem key={option} value={option.toString()}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <span className="text-sm text-muted-foreground hidden sm:inline">per page</span>
              </div>
            )}

            {/* Navigation Controls */}
            <div className="flex items-center gap-1 justify-center">
              {/* Previous Button */}
              <Button
                variant="outline"
                size="sm"
                disabled={!pagination.hasPreviousPage || isLoading}
                onClick={() => setPage(page - 1)}
                className="px-3"
              >
                <ChevronLeft className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">Previous</span>
              </Button>

              {/* Page Numbers */}
              <div className="flex items-center gap-1">
                {(() => {
                  const currentPage = pagination.page;
                  const totalPages = pagination.totalPages;
                  const maxVisible = shouldShowMobile ? 3 : 5;

                  let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
                  const endPage = Math.min(totalPages, startPage + maxVisible - 1);

                  // Adjust if we're near the end
                  if (endPage - startPage + 1 < maxVisible) {
                    startPage = Math.max(1, endPage - maxVisible + 1);
                  }

                  const pages = [];

                  // Add first page and ellipsis if needed
                  if (startPage > 1) {
                    pages.push(
                      <Button
                        key={1}
                        variant={1 === currentPage ? 'default' : 'outline'}
                        size="sm"
                        disabled={isLoading}
                        onClick={() => setPage(1)}
                        className="w-8 h-8 p-0"
                      >
                        1
                      </Button>
                    );

                    if (startPage > 2) {
                      pages.push(
                        <span key="start-ellipsis" className="text-muted-foreground px-1">
                          ...
                        </span>
                      );
                    }
                  }

                  // Add visible page numbers
                  for (let i = startPage; i <= endPage; i++) {
                    pages.push(
                      <Button
                        key={i}
                        variant={i === currentPage ? 'default' : 'outline'}
                        size="sm"
                        disabled={isLoading}
                        onClick={() => setPage(i)}
                        className="w-8 h-8 p-0"
                      >
                        {i}
                      </Button>
                    );
                  }

                  // Add ellipsis and last page if needed
                  if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                      pages.push(
                        <span key="end-ellipsis" className="text-muted-foreground px-1">
                          ...
                        </span>
                      );
                    }

                    pages.push(
                      <Button
                        key={totalPages}
                        variant={totalPages === currentPage ? 'default' : 'outline'}
                        size="sm"
                        disabled={isLoading}
                        onClick={() => setPage(totalPages)}
                        className="w-8 h-8 p-0"
                      >
                        {totalPages}
                      </Button>
                    );
                  }

                  return pages;
                })()}
              </div>

              {/* Next Button */}
              <Button
                variant="outline"
                size="sm"
                disabled={!pagination.hasNextPage || isLoading}
                onClick={() => setPage(page + 1)}
                className="px-3"
              >
                <span className="hidden sm:inline">Next</span>
                <ChevronRight className="h-4 w-4 sm:ml-2" />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Helper function to create sortable column header
export function SortableHeader({
  column,
  children,
  className,
}: {
  column?: unknown;
  children: React.ReactNode;
  className?: string;
}) {
  const { sortBy, sortOrder, setSorting } = useTableState();

  // Get sort key from accessorKey
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const sortKey = (column as any)?.columnDef?.accessorKey
    ? // eslint-disable-next-line @typescript-eslint/no-explicit-any
      String((column as any).columnDef.accessorKey)
    : undefined;

  const handleSort = () => {
    if (!sortKey) return;

    // Determine new sort order
    const currentlySorted = sortBy === sortKey;
    const newSortOrder = currentlySorted && sortOrder === 'asc' ? 'desc' : 'asc';

    setSorting(sortKey, newSortOrder);
  };

  const getSortIcon = () => {
    if (sortKey && sortBy === sortKey) {
      return <span className="text-primary">{sortOrder === 'asc' ? '↑' : '↓'}</span>;
    }
    return <ArrowUpDown className="h-4 w-4 opacity-50" />;
  };

  return (
    <div
      onClick={handleSort}
      className={`flex cursor-pointer items-center hover:bg-muted/50 px-2 py-1 rounded transition-colors select-none ${className || ''}`}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleSort();
        }
      }}
    >
      <span>{children}</span>
      <span className="ml-1 flex-shrink-0">{getSortIcon()}</span>
    </div>
  );
}

// Helper function to create action column
export function ActionsCell({ children }: { children: React.ReactNode }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">{children}</DropdownMenuContent>
    </DropdownMenu>
  );
}
