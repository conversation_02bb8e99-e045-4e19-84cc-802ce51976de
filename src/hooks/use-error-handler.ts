import { useRouter } from 'next/navigation';
import { useCallback } from 'react';

export interface ErrorInfo {
  type: 'not-found' | 'forbidden' | 'unauthorized' | 'server-error' | 'network-error';
  message?: string;
  digest?: string;
  statusCode?: number;
}

export function useErrorHandler() {
  const router = useRouter();

  const handleError = useCallback(
    (error: ErrorInfo) => {
      // Log error for monitoring
      console.error('Application error:', error);

      // Navigate to appropriate error page based on error type
      switch (error.type) {
        case 'not-found':
          router.push('/not-found');
          break;
        case 'forbidden':
          router.push('/forbidden');
          break;
        case 'unauthorized':
          router.push('/unauthorized');
          break;
        case 'server-error':
        case 'network-error':
        default:
          // For server errors, trigger the error boundary instead of navigation
          throw new Error(error.message || 'An unexpected error occurred');
      }
    },
    [router]
  );

  const handleHttpError = useCallback(
    (statusCode: number, message?: string) => {
      const errorMap: Record<number, ErrorInfo['type']> = {
        401: 'unauthorized',
        403: 'forbidden',
        404: 'not-found',
        500: 'server-error',
        502: 'server-error',
        503: 'server-error',
        504: 'server-error',
      };

      const errorType = errorMap[statusCode] || 'server-error';
      handleError({
        type: errorType,
        message,
        statusCode,
      });
    },
    [handleError]
  );

  return {
    handleError,
    handleHttpError,
  };
}
