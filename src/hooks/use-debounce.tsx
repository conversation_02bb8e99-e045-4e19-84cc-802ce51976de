import React from 'react';

export default function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = React.useState(value);

  React.useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

//  how to use it with state or props
// const [searchTerm, setSearchTerm] = React.useState('');
// import useDebounce from './use-debounce';
//
// const debouncedSearchTerm = useDebounce(searchTerm, 500);
