import type { OrganizationFilters } from '@/@types/organization';
import { api } from '@/trpc/react';

export interface UseOrganizationsOptions extends OrganizationFilters {
  enabled?: boolean;
  staleTime?: number;
  refetchOnWindowFocus?: boolean;
}

export function useOrganizations(options: UseOrganizationsOptions = {}) {
  const {
    page = 1,
    limit = 10,
    search,
    sortBy = 'name',
    sortOrder = 'asc',
    enabled = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus = false,
    checkAdmin = false,
    organizationId,
  } = options;

  return api.organization.getAllOrganizations.useQuery(
    {
      page,
      limit,
      search,
      sortBy,
      sortOrder,
      organizationId,
      checkAdmin,
    },
    {
      enabled,
      staleTime,
      refetchOnWindowFocus,
    }
  );
}

// Hook for creating organizations
export function useCreateOrganization() {
  const utils = api.useUtils();

  return api.organization.createOrganization.useMutation({
    onSuccess: () => {
      // Invalidate and refetch the organizations list
      utils.organization.getAllOrganizations.invalidate();
    },
  });
}

// Hook for getting a single organization by ID
export function useOrganization(id: string, enabled = true) {
  return api.organization.getOrganizationById.useQuery(id, {
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for updating organizations
export function useUpdateOrganization() {
  const utils = api.useUtils();

  return api.organization.updateOrganization.useMutation({
    onSuccess: () => {
      // Invalidate and refetch the organizations list
      utils.organization.getAllOrganizations.invalidate();
    },
  });
}

// Hook for getting organization members
export function useOrganizationMembers(options: {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: 'name' | 'email' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  enabled?: boolean;
  staleTime?: number;
  refetchOnWindowFocus?: boolean;
  organizationId?: string | undefined; // Required field
}) {
  const {
    page = 1,
    limit = 10,
    search,
    sortBy = 'name',
    sortOrder = 'asc',
    staleTime = 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus = false,
    organizationId,
    enabled = true, // Default to true unless specified
  } = options;

  return api.organization.getOrganizationMembers.useQuery(
    {
      page,
      limit,
      search,
      sortBy,
      sortOrder,
      organizationId,
    },
    {
      enabled,
      staleTime,
      refetchOnWindowFocus,
    }
  );
}
