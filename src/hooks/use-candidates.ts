import type { CandidateFilters } from '@/@types/candidate';
import { api } from '@/trpc/react';

export interface UseCandidatesOptions extends CandidateFilters {
  enabled?: boolean;
  staleTime?: number;
  refetchOnWindowFocus?: boolean;
}

export function useCandidates(options: UseCandidatesOptions = {}) {
  const {
    page = 1,
    limit = 10,
    search,
    sortBy = 'name',
    sortOrder = 'asc',
    enabled = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus = false,
  } = options;

  return api.candidate.getAllCandidates.useQuery(
    {
      page,
      limit,
      search,
      sortBy,
      sortOrder,
    },
    {
      enabled,
      staleTime,
      refetchOnWindowFocus,
    }
  );
}

// Hook for creating candidates
export function useCreateCandidate() {
  const utils = api.useUtils();

  return api.candidate.createCandidate.useMutation({
    onSuccess: () => {
      // Invalidate and refetch the candidates list
      utils.candidate.getAllCandidates.invalidate();
    },
  });
}

// Hook for getting a single candidate by ID
export function useCandidate(id: string, enabled = true) {
  return api.candidate.getCandidateById.useQuery(id, {
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for updating candidates
export function useUpdateCandidate() {
  const utils = api.useUtils();

  return api.candidate.updateCandidate.useMutation({
    onSuccess: () => {
      // Invalidate and refetch the candidates list
      utils.candidate.getAllCandidates.invalidate();
    },
  });
}
