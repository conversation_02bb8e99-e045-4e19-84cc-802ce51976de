import { api } from '@/trpc/react';

export interface PortalRequestFilters {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?:
    | 'createdAt'
    | 'updatedAt'
    | 'full_name'
    | 'work_email'
    | 'company_name'
    | 'company_size'
    | 'industry'
    | 'status';
  sortOrder?: 'asc' | 'desc';
}

export interface UsePortalRequestOptions extends PortalRequestFilters {
  enabled?: boolean;
  staleTime?: number;
  refetchOnWindowFocus?: boolean;
}

export function usePortalRequests(options: UsePortalRequestOptions = {}) {
  const {
    page = 1,
    limit = 10,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    enabled = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus = false,
  } = options;

  const validSortByValues = [
    'createdAt',
    'updatedAt',
    'full_name',
    'work_email',
    'company_name',
    'company_size',
    'industry',
    'status',
  ];
  if (!validSortByValues.includes(sortBy)) {
    throw new Error(
      `Invalid sortBy value. Expected one of ${validSortByValues.join(', ')}, received '${sortBy}'.`
    );
  }

  return api.portalRequest.getAllPortalRequests.useQuery(
    {
      page,
      limit,
      search,
      sortBy,
      sortOrder,
    },
    {
      enabled,
      staleTime,
      refetchOnWindowFocus,
    }
  );
}

export function useCreatePortalRequest() {
  const utils = api.useUtils();

  return api.portalRequest.createPortalRequest.useMutation({
    onSuccess: () => {
      // Invalidate and refetch the portal requests list
      utils.portalRequest.getAllPortalRequests.invalidate();
    },
  });
}

export function usePortalRequest(id: string, enabled = true) {
  return api.portalRequest.getPortalRequestById.useQuery(id, {
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
