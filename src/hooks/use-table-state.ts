import { parseAsInteger, parseAsString, useQueryStates } from 'nuqs';
import { useMemo } from 'react';

export type SortOrder = 'asc' | 'desc';

export interface UseTableStateOptions<TSortField extends string = string> {
  defaultPage?: number;
  defaultLimit?: number;
  defaultSearch?: string;
  defaultSortBy?: TSortField;
  defaultSortOrder?: SortOrder;
}

export function useTableState<TSortField extends string = string>(
  options: UseTableStateOptions<TSortField> = {}
) {
  const {
    defaultPage = 1,
    defaultLimit = 10,
    defaultSearch = '',
    defaultSortBy = 'name' as TSortField,
    defaultSortOrder = 'asc' as SortOrder,
  } = options;

  // Use nuqs to manage URL state
  const [state, setState] = useQueryStates({
    page: parseAsInteger.withDefault(defaultPage),
    limit: parseAsInteger.withDefault(defaultLimit),
    search: parseAsString.withDefault(defaultSearch),
    sortBy: parseAsString.withDefault(defaultSortBy),
    sortOrder: parseAsString.withDefault(defaultSortOrder),
  });

  // Memoized handlers to prevent unnecessary re-renders
  const handlers = useMemo(
    () => ({
      setPage: (page: number) => setState({ page }),
      setLimit: (limit: number) => setState({ limit, page: 1 }), // Reset to first page when changing limit
      setSearch: (search: string) => setState({ search, page: 1 }), // Reset to first page when searching
      setSorting: (sortBy: TSortField, sortOrder: SortOrder) =>
        setState({ sortBy, sortOrder, page: 1 }), // Reset to first page when sorting
      reset: () =>
        setState({
          page: defaultPage,
          limit: defaultLimit,
          search: defaultSearch,
          sortBy: defaultSortBy,
          sortOrder: defaultSortOrder,
        }),
    }),
    [setState, defaultPage, defaultLimit, defaultSearch, defaultSortBy, defaultSortOrder]
  );

  return {
    // Current state
    page: state.page,
    limit: state.limit,
    search: state.search,
    sortBy: state.sortBy as TSortField,
    sortOrder: state.sortOrder as SortOrder,

    // Handlers
    ...handlers,

    // Convenience getter for API params
    apiParams: {
      page: state.page,
      limit: state.limit,
      search: state.search || undefined,
      sortBy: state.sortBy as TSortField,
      sortOrder: state.sortOrder as SortOrder,
    },
  };
}
