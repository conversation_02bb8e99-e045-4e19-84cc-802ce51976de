import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { NuqsAdapter } from 'nuqs/adapters/next/app';

import { TRPCReactProvider } from '@/trpc/react';

import { ThemeProvider } from './_theme-provider';
interface RootProviderProps {
  children: React.ReactNode;
}

export default async function RootProvider({ children }: RootProviderProps) {
  const messages = await getMessages();
  return (
    <NextIntlClientProvider messages={messages}>
      <TRPCReactProvider>
        <NuqsAdapter>
          <ThemeProvider
            attribute="class"
            defaultTheme="dark"
            enableSystem
            disableTransitionOnChange
          >
            {children}
          </ThemeProvider>
        </NuqsAdapter>
      </TRPCReactProvider>
    </NextIntlClientProvider>
  );
}
