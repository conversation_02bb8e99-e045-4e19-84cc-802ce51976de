'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

import type { IOrganization } from '@/@types';

export type UserRole = 'admin' | 'recruiter' | 'candidate';

export type Organization = Pick<IOrganization, 'id' | 'name'> & { logo?: string };

interface RoleContextType {
  role: UserRole;
  setRole: (role: UserRole) => void;
  organization: Organization | null;
  setOrganization: (org: Organization) => void;
  organizations: Organization[];
  setOrganizations: (orgs: Organization[]) => void;
}

const RoleContext = createContext<RoleContextType | undefined>(undefined);

export function RoleProvider({ children }: { children: React.ReactNode }) {
  const [role, setRole] = useState<UserRole>('admin'); // Default to admin for demo
  const [organization, setOrganization] = useState<Organization | null>({
    id: '1',
    name: 'Hirelytics Inc.',
  });
  const [organizations, setOrganizations] = useState<Organization[]>([
    { id: '1', name: 'Hirelytics Inc.' },
    { id: '2', name: 'TechCorp Ltd.' },
    { id: '3', name: 'StartupXYZ' },
  ]);

  // Load from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedRole = localStorage.getItem('userRole') as UserRole;
      const savedOrg = localStorage.getItem('currentOrganization');
      const savedOrgs = localStorage.getItem('userOrganizations');

      if (savedRole) setRole(savedRole);
      if (savedOrg) setOrganization(JSON.parse(savedOrg));
      if (savedOrgs) setOrganizations(JSON.parse(savedOrgs));
    }
  }, []);

  // Save to localStorage when values change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('userRole', role);
    }
  }, [role]);

  useEffect(() => {
    if (typeof window !== 'undefined' && organization) {
      localStorage.setItem('currentOrganization', JSON.stringify(organization));
    }
  }, [organization]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('userOrganizations', JSON.stringify(organizations));
    }
  }, [organizations]);

  return (
    <RoleContext.Provider
      value={{
        role,
        setRole,
        organization,
        setOrganization,
        organizations,
        setOrganizations,
      }}
    >
      {children}
    </RoleContext.Provider>
  );
}

export function useRole() {
  const context = useContext(RoleContext);
  if (context === undefined) {
    throw new Error('useRole must be used within a RoleProvider');
  }
  return context;
}
