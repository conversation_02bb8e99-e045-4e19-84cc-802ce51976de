'use client';
import { createContext, useCallback, useEffect, useState } from 'react';

import { IUser } from '@/@types';
import { Session } from '@/auth';
import { authClient } from '@/auth/client';

interface AuthContextType {
  session: Session | null;
  user: IUser | null;
  logout: () => Promise<void>;
}
export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<IUser | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  const fetchSession = useCallback(async () => {
    setLoading(true);
    try {
      const { data } = await authClient.getSession();
      console.log(data);
      setSession(data?.session as unknown as Session | null);
      setUser(data?.user as unknown as IUser | null);
      setLoading(false);
    } catch (error) {
      console.error(error);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  }, []);

  const logout = useCallback(async () => {
    await authClient.signOut();
    setSession(null);
    setUser(null);
  }, []);

  useEffect(() => {
    fetchSession();
  }, [fetchSession]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return <AuthContext.Provider value={{ session, user, logout }}>{children}</AuthContext.Provider>;
}
