import type { NextConfig } from 'next';
import createNextIntlPlugin from 'next-intl/plugin';

import { env } from './src/env';

const withNextIntl = createNextIntlPlugin();

// Get allowed dev origins from environment
const getAllowedDevOrigins = (): string[] => {
  if (env.NODE_ENV === 'production') {
    return [`*.${env.PRODUCTION_APP_DOMAIN}`];
  }

  // For development, extract domains from cookie domains and trusted origins
  const cookieDomains = env.DEV_COOKIE_DOMAINS.split(',').map((domain) => domain.trim());
  const allowedOrigins = cookieDomains
    .filter((domain) => domain.startsWith('.'))
    .map((domain) => `*${domain}`);

  // Add any additional patterns
  return [...allowedOrigins, '*.localhost.me', '*.hirelytics.me'];
};

const nextConfig: NextConfig = {
  /* config options here */
  allowedDevOrigins: getAllowedDevOrigins(),
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'example.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
};

export default withNextIntl(nextConfig);
